/**
 * ESLint rule to enforce that functions return Result type
 * @fileoverview Require functions to return Result type for Railway Oriented Programming
 */

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Require functions to return Result type',
      category: 'Best Practices',
      recommended: true,
    },
    fixable: null,
    schema: [
      {
        type: 'object',
        properties: {
          allowedReturnTypes: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          exemptFunctions: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          exemptPatterns: {
            type: 'array',
            items: {
              type: 'string'
            }
          }
        },
        additionalProperties: false
      }
    ],
    messages: {
      requireResultType: 'Function "{{name}}" must return Result<T, E> type. Current return type: {{currentType}}',
      missingReturnType: 'Function "{{name}}" must have an explicit return type annotation that returns Result<T, E>',
    }
  },

  create(context) {
    const options = context.options[0] || {};
    const allowedReturnTypes = options.allowedReturnTypes || ['void', 'Promise<void>', 'never'];
    const exemptFunctions = options.exemptFunctions || ['main', 'setup', 'teardown'];
    const exemptPatterns = options.exemptPatterns || [];

    function normalizeTypeText(typeText) {
      // Remove whitespace and normalize the type text
      return typeText.replace(/\s+/g, ' ').trim();
    }

    function isResultType(typeAnnotation) {
      if (!typeAnnotation) return false;

      const sourceCode = context.getSourceCode();
      const typeText = normalizeTypeText(sourceCode.getText(typeAnnotation));

      // More precise Result type detection
      const resultPatterns = [
        /^Result<[^>]+,\s*[^>]+>$/,           // Result<T, E>
        /^Result<[^>]+>$/,                    // Result<T> (with default error type)
        /^Promise<Result<[^>]+,\s*[^>]+>>$/,  // Promise<Result<T, E>>
        /^Promise<Result<[^>]+>>$/            // Promise<Result<T>>
      ];

      return resultPatterns.some(pattern => pattern.test(typeText));
    }

    function isAllowedReturnType(typeAnnotation) {
      if (!typeAnnotation) return false;

      const sourceCode = context.getSourceCode();
      const typeText = normalizeTypeText(sourceCode.getText(typeAnnotation));

      return allowedReturnTypes.some(allowed => {
        const normalizedAllowed = normalizeTypeText(allowed);
        return typeText === normalizedAllowed ||
               typeText.includes(normalizedAllowed) ||
               new RegExp(`^${normalizedAllowed.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`).test(typeText);
      });
    }

    function isExemptFunction(functionName) {
      // Check exact function name matches
      if (exemptFunctions.includes(functionName)) {
        return true;
      }

      // Check for built-in utility functions
      const builtInUtilities = ['isOk', 'isErr', 'constructor'];
      if (builtInUtilities.includes(functionName)) {
        return true;
      }

      // Check pattern matches
      if (exemptPatterns.some(pattern => {
        const regex = new RegExp(pattern);
        return regex.test(functionName);
      })) {
        return true;
      }

      // Check if it's in a test file
      const filename = context.getFilename();
      if (filename.includes('.spec.') || filename.includes('.test.')) {
        return true;
      }

      return false;
    }

    function getFunctionName(node) {
      if (node.id && node.id.name) {
        return node.id.name;
      }
      if (node.key && node.key.name) {
        return node.key.name;
      }
      if (node.parent && node.parent.type === 'VariableDeclarator' && node.parent.id.name) {
        return node.parent.id.name;
      }
      if (node.parent && node.parent.type === 'Property' && node.parent.key.name) {
        return node.parent.key.name;
      }
      if (node.parent && node.parent.type === 'AssignmentExpression' && node.parent.left.name) {
        return node.parent.left.name;
      }
      return 'anonymous';
    }

    function shouldCheckFunction(node) {
      // Skip if function has no body (interface/type definitions)
      if (!node.body) {
        return false;
      }

      // Skip if it's a constructor (method definition)
      if (node.kind === 'constructor') {
        return false;
      }

      // Skip if it's a getter/setter
      if (node.kind === 'get' || node.kind === 'set') {
        return false;
      }

      // Skip if it's a constructor function (MethodDefinition with key name 'constructor')
      if (node.type === 'MethodDefinition' && node.key && node.key.name === 'constructor') {
        return false;
      }

      return true;
    }

    function checkFunction(node) {
      if (!shouldCheckFunction(node)) {
        return;
      }

      const functionName = getFunctionName(node);

      // Skip exempt functions
      if (isExemptFunction(functionName)) {
        return;
      }

      const returnType = node.returnType;

      if (!returnType) {
        context.report({
          node,
          messageId: 'missingReturnType',
          data: {
            name: functionName
          }
        });
        return;
      }

      const typeAnnotation = returnType.typeAnnotation;

      if (!isResultType(typeAnnotation) && !isAllowedReturnType(typeAnnotation)) {
        const sourceCode = context.getSourceCode();
        const currentType = sourceCode.getText(typeAnnotation);

        context.report({
          node: returnType,
          messageId: 'requireResultType',
          data: {
            name: functionName,
            currentType: currentType
          }
        });
      }
    }

    return {
      FunctionDeclaration: checkFunction,
      FunctionExpression: checkFunction,
      ArrowFunctionExpression: checkFunction,
      MethodDefinition: checkFunction,
    };
  }
};
