{"name": "server", "scripts": {"dev": "bun run --hot src/index.ts", "test": "vitest", "test:run": "vitest run"}, "dependencies": {"@prisma/client": "^6.8.2", "hono": "^4.7.10", "uuid": "^11.1.0", "velona": "^0.8.0", "zod": "^3.25.28"}, "devDependencies": {"@eslint/js": "^9.27.0", "@prisma/cli-init": "^0.1.0", "@types/bun": "latest", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-local-rules": "^3.0.2", "eslint-plugin-react": "^7.37.5", "globals": "^16.2.0", "prettier": "^3.5.3", "prisma": "^6.8.2", "typescript-eslint": "^8.32.1", "vitest": "^3.1.4"}, "module": "src/index.ts", "type": "module", "private": true, "peerDependencies": {"typescript": "^5"}}