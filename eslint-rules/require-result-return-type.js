/**
 * ESLint rule to enforce that functions return Result type
 * @fileoverview Require functions to return Result type for Railway Oriented Programming
 */

module.exports = {
  meta: {
    type: 'problem',
    docs: {
      description: 'Require functions to return Result type',
      category: 'Best Practices',
      recommended: true,
    },
    fixable: null,
    schema: [
      {
        type: 'object',
        properties: {
          allowedReturnTypes: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          exemptFunctions: {
            type: 'array',
            items: {
              type: 'string'
            }
          }
        },
        additionalProperties: false
      }
    ],
    messages: {
      requireResultType: 'Function "{{name}}" must return Result<T, E> type. Current return type: {{currentType}}',
      missingReturnType: 'Function "{{name}}" must have an explicit return type annotation that returns Result<T, E>',
    }
  },

  create(context) {
    const options = context.options[0] || {};
    const allowedReturnTypes = options.allowedReturnTypes || ['void', 'Promise<void>', 'never'];
    const exemptFunctions = options.exemptFunctions || ['main', 'setup', 'teardown'];

    function isResultType(typeAnnotation) {
      if (!typeAnnotation) return false;
      
      const sourceCode = context.getSourceCode();
      const typeText = sourceCode.getText(typeAnnotation);
      
      // Check if it's a Result type
      return /^Result<.*>$/.test(typeText) || 
             /^Promise<Result<.*>>$/.test(typeText);
    }

    function isAllowedReturnType(typeAnnotation) {
      if (!typeAnnotation) return false;
      
      const sourceCode = context.getSourceCode();
      const typeText = sourceCode.getText(typeAnnotation);
      
      return allowedReturnTypes.some(allowed => 
        typeText === allowed || typeText.includes(allowed)
      );
    }

    function isExemptFunction(functionName) {
      return exemptFunctions.includes(functionName);
    }

    function getFunctionName(node) {
      if (node.id && node.id.name) {
        return node.id.name;
      }
      if (node.key && node.key.name) {
        return node.key.name;
      }
      if (node.parent && node.parent.type === 'VariableDeclarator' && node.parent.id.name) {
        return node.parent.id.name;
      }
      if (node.parent && node.parent.type === 'Property' && node.parent.key.name) {
        return node.parent.key.name;
      }
      return 'anonymous';
    }

    function checkFunction(node) {
      const functionName = getFunctionName(node);
      
      // Skip exempt functions
      if (isExemptFunction(functionName)) {
        return;
      }

      // Skip if function has no body (interface/type definitions)
      if (!node.body) {
        return;
      }

      const returnType = node.returnType;
      
      if (!returnType) {
        context.report({
          node,
          messageId: 'missingReturnType',
          data: {
            name: functionName
          }
        });
        return;
      }

      const typeAnnotation = returnType.typeAnnotation;
      
      if (!isResultType(typeAnnotation) && !isAllowedReturnType(typeAnnotation)) {
        const sourceCode = context.getSourceCode();
        const currentType = sourceCode.getText(typeAnnotation);
        
        context.report({
          node: returnType,
          messageId: 'requireResultType',
          data: {
            name: functionName,
            currentType: currentType
          }
        });
      }
    }

    return {
      FunctionDeclaration: checkFunction,
      FunctionExpression: checkFunction,
      ArrowFunctionExpression: checkFunction,
      MethodDefinition: checkFunction,
    };
  }
};
