import globals from "globals";
import tseslint from "typescript-eslint";
import pluginReact from "eslint-plugin-react";
import prettier from "eslint-config-prettier";
import localRules from "eslint-plugin-local-rules";
import { defineConfig } from "eslint/config";


export default defineConfig([
  { files: ["**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}"], languageOptions: { globals: { ...globals.browser, ...globals.node } } },
  ...tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  prettier,
  {
    files: ["src/**/*.{ts,tsx}"],
    ignores: ["src/**/*.spec.ts", "src/**/*.test.ts"],
    plugins: {
      "local-rules": localRules,
    },
    rules: {
      "local-rules/require-result-return-type": ["error", {
        allowedReturnTypes: [
          "void",
          "Promise<void>",
          "never"
        ],
        exemptFunctions: [
          "main",
          "setup",
          "teardown",
          "describe",
          "it",
          "expect",
          "beforeEach",
          "afterEach",
          "beforeAll",
          "afterAll",
          "console.log",
          "console.error",
          "console.warn",
          "console.info"
        ],
        exemptPatterns: [
          "^test.*",
          "^spec.*",
          ".*Test$",
          ".*Spec$",
          "^mock.*",
          "^stub.*"
        ]
      }]
    }
  }
]);
