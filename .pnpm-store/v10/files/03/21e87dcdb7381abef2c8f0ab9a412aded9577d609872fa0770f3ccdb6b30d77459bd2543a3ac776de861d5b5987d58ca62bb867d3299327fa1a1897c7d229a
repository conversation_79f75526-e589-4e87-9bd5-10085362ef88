/** @internal */
export const OP_ASYNC = "Async";
/** @internal */
export const OP_COMMIT = "Commit";
/** @internal */
export const OP_FAILURE = "Failure";
/** @internal */
export const OP_ON_FAILURE = "OnFailure";
/** @internal */
export const OP_ON_SUCCESS = "OnSuccess";
/** @internal */
export const OP_ON_SUCCESS_AND_FAILURE = "OnSuccessAndFailure";
/** @internal */
export const OP_SUCCESS = "Success";
/** @internal */
export const OP_SYNC = "Sync";
/** @internal */
export const OP_TAG = "Tag";
/** @internal */
export const OP_UPDATE_RUNTIME_FLAGS = "UpdateRuntimeFlags";
/** @internal */
export const OP_WHILE = "While";
/** @internal */
export const OP_ITERATOR = "Iterator";
/** @internal */
export const OP_WITH_RUNTIME = "WithRuntime";
/** @internal */
export const OP_YIELD = "Yield";
/** @internal */
export const OP_REVERT_FLAGS = "RevertFlags";
//# sourceMappingURL=effect.js.map