{"version": 3, "file": "singleProducerAsyncInput.js", "names": ["Cause", "Deferred", "Effect", "Either", "Exit", "pipe", "Ref", "OP_STATE_EMPTY", "OP_STATE_EMIT", "OP_STATE_ERROR", "OP_STATE_DONE", "stateEmpty", "notifyProducer", "_tag", "stateEmit", "notifyConsumers", "stateError", "cause", "stateDone", "done", "SingleProducerAsyncInputImpl", "ref", "constructor", "awaitR<PERSON>", "flatten", "modify", "state", "await", "void", "close", "fiberIdWith", "fiberId", "error", "interrupt", "value", "for<PERSON>ach", "deferred", "succeed", "left", "discard", "emit", "element", "flatMap", "make", "notifyConsumer", "slice", "undefined", "right", "length", "Error", "failCause", "take", "take<PERSON><PERSON>", "map", "elem", "fail", "onError", "onElement", "onDone", "zipRight", "matchCause", "onFailure", "onSuccess", "match", "onLeft", "onRight"], "sources": ["../../../../src/internal/channel/singleProducerAsyncInput.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,QAAQ,MAAM,mBAAmB;AAC7C,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,OAAO,KAAKC,IAAI,MAAM,eAAe;AACrC,SAASC,IAAI,QAAQ,mBAAmB;AACxC,OAAO,KAAKC,GAAG,MAAM,cAAc;AAUnC;AACA,MAAMC,cAAc,GAAG,OAAgB;AAKvC;AACA,MAAMC,aAAa,GAAG,MAAe;AAKrC;AACA,MAAMC,cAAc,GAAG,OAAgB;AAKvC;AACA,MAAMC,aAAa,GAAG,MAAe;AA6BrC;AACA,MAAMC,UAAU,GAAIC,cAAuC,KAAkC;EAC3FC,IAAI,EAAEN,cAAc;EACpBK;CACD,CAAC;AAEF;AACA,MAAME,SAAS,GACbC,eAAiF,KACrD;EAC5BF,IAAI,EAAEL,aAAa;EACnBO;CACD,CAAC;AAEF;AACA,MAAMC,UAAU,GAASC,KAAuB,KAAgC;EAC9EJ,IAAI,EAAEJ,cAAc;EACpBQ;CACD,CAAC;AAEF;AACA,MAAMC,SAAS,GAAUC,IAAU,KAAiC;EAClEN,IAAI,EAAEH,aAAa;EACnBS;CACD,CAAC;AAEF;AACA,MAAMC,4BAA4B;EAGXC,GAAA;EAArBC,YAAqBD,GAAoC;IAApC,KAAAA,GAAG,GAAHA,GAAG;EACxB;EAEAE,SAASA,CAAA;IACP,OAAOrB,MAAM,CAACsB,OAAO,CACnBlB,GAAG,CAACmB,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IACzBA,KAAK,CAACb,IAAI,KAAKN,cAAc,GAC3B,CAACN,QAAQ,CAAC0B,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAA+B,CAAC,GACvE,CAACxB,MAAM,CAAC0B,IAAI,EAAEF,KAAK,CAAC,CAAC,CAC1B;EACH;EAEA,IAAIG,KAAKA,CAAA;IACP,OAAO3B,MAAM,CAAC4B,WAAW,CAAEC,OAAO,IAAK,IAAI,CAACC,KAAK,CAAChC,KAAK,CAACiC,SAAS,CAACF,OAAO,CAAC,CAAC,CAAC;EAC9E;EAEAZ,IAAIA,CAACe,KAAW;IACd,OAAOhC,MAAM,CAACsB,OAAO,CACnBlB,GAAG,CAACmB,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CAACN,QAAQ,CAAC0B,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAAK,CAAC;UACtD;QACA,KAAKlB,aAAa;UAAE;YAClB,OAAO,CACLN,MAAM,CAACiC,OAAO,CACZT,KAAK,CAACX,eAAe,EACpBqB,QAAQ,IAAKnC,QAAQ,CAACoC,OAAO,CAACD,QAAQ,EAAEjC,MAAM,CAACmC,IAAI,CAACJ,KAAK,CAAC,CAAC,EAC5D;cAAEK,OAAO,EAAE;YAAI,CAAE,CAClB,EACDrB,SAAS,CAACgB,KAAK,CAA2B,CAC3C;UACH;QACA,KAAKzB,cAAc;UAAE;YACnB,OAAO,CAACP,MAAM,CAAC+B,SAAS,EAAEP,KAAK,CAAC;UAClC;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAACR,MAAM,CAAC+B,SAAS,EAAEP,KAAK,CAAC;UAClC;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEAc,IAAIA,CAACC,OAAa;IAChB,OAAOvC,MAAM,CAACwC,OAAO,CAACzC,QAAQ,CAAC0C,IAAI,EAAQ,EAAGP,QAAQ,IACpDlC,MAAM,CAACsB,OAAO,CACZlB,GAAG,CAACmB,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CAACN,QAAQ,CAAC0B,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAAK,CAAC;UACtD;QACA,KAAKlB,aAAa;UAAE;YAClB,MAAMoC,cAAc,GAAGlB,KAAK,CAACX,eAAe,CAAC,CAAC,CAAC;YAC/C,MAAMA,eAAe,GAAGW,KAAK,CAACX,eAAe,CAAC8B,KAAK,CAAC,CAAC,CAAC;YACtD,IAAID,cAAc,KAAKE,SAAS,EAAE;cAChC,OAAO,CACL7C,QAAQ,CAACoC,OAAO,CAACO,cAAc,EAAEzC,MAAM,CAAC4C,KAAK,CAACN,OAAO,CAAC,CAAC,EACtD1B,eAAe,CAACiC,MAAM,KAAK,CAAC,GAC3BrC,UAAU,CAACyB,QAAQ,CAAC,GACpBtB,SAAS,CAACC,eAAe,CAAC,CAC7B;YACH;YACA,MAAM,IAAIkC,KAAK,CACb,oIAAoI,CACrI;UACH;QACA,KAAKxC,cAAc;UAAE;YACnB,OAAO,CAACP,MAAM,CAAC+B,SAAS,EAAEP,KAAK,CAAC;UAClC;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAACR,MAAM,CAAC+B,SAAS,EAAEP,KAAK,CAAC;UAClC;MACF;IACF,CAAC,CAAC,CACH,CAAC;EACN;EAEAM,KAAKA,CAACf,KAAuB;IAC3B,OAAOf,MAAM,CAACsB,OAAO,CACnBlB,GAAG,CAACmB,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CAACN,QAAQ,CAAC0B,KAAK,CAACD,KAAK,CAACd,cAAc,CAAC,EAAEc,KAAK,CAAC;UACtD;QACA,KAAKlB,aAAa;UAAE;YAClB,OAAO,CACLN,MAAM,CAACiC,OAAO,CACZT,KAAK,CAACX,eAAe,EACpBqB,QAAQ,IAAKnC,QAAQ,CAACiD,SAAS,CAACd,QAAQ,EAAEnB,KAAK,CAAC,EACjD;cAAEsB,OAAO,EAAE;YAAI,CAAE,CAClB,EACDvB,UAAU,CAACC,KAAK,CAA2B,CAC5C;UACH;QACA,KAAKR,cAAc;UAAE;YACnB,OAAO,CAACP,MAAM,CAAC+B,SAAS,EAAEP,KAAK,CAAC;UAClC;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAACR,MAAM,CAAC+B,SAAS,EAAEP,KAAK,CAAC;UAClC;MACF;IACF,CAAC,CAAC,CACH;EACH;EAEA,IAAIyB,IAAIA,CAAA;IACN,OAAO,IAAI,CAACC,QAAQ,CACjBnC,KAAK,IAAKb,IAAI,CAAC8C,SAAS,CAAClD,KAAK,CAACqD,GAAG,CAACpC,KAAK,EAAEd,MAAM,CAACmC,IAAI,CAAC,CAAC,EACvDgB,IAAI,IAAKlD,IAAI,CAACiC,OAAO,CAACiB,IAAI,CAA8C,EACxEnC,IAAI,IAAKf,IAAI,CAACmD,IAAI,CAACpD,MAAM,CAAC4C,KAAK,CAAC5B,IAAI,CAAC,CAAC,CACxC;EACH;EAEAiC,QAAQA,CACNI,OAAuC,EACvCC,SAA+B,EAC/BC,MAA0B;IAE1B,OAAOxD,MAAM,CAACwC,OAAO,CAACzC,QAAQ,CAAC0C,IAAI,EAAkC,EAAGP,QAAQ,IAC9ElC,MAAM,CAACsB,OAAO,CACZlB,GAAG,CAACmB,MAAM,CAAC,IAAI,CAACJ,GAAG,EAAGK,KAAK,IAAI;MAC7B,QAAQA,KAAK,CAACb,IAAI;QAChB,KAAKN,cAAc;UAAE;YACnB,OAAO,CACLL,MAAM,CAACyD,QAAQ,CACb1D,QAAQ,CAACoC,OAAO,CAACX,KAAK,CAACd,cAAc,EAAE,KAAK,CAAC,CAAC,EAC9CV,MAAM,CAAC0D,UAAU,CAAC3D,QAAQ,CAAC0B,KAAK,CAACS,QAAQ,CAAC,EAAE;cAC1CyB,SAAS,EAAEL,OAAO;cAClBM,SAAS,EAAE3D,MAAM,CAAC4D,KAAK,CAAC;gBAAEC,MAAM,EAAEN,MAAM;gBAAEO,OAAO,EAAER;cAAS,CAAE;aAC/D,CAAC,CACH,EACD3C,SAAS,CAAC,CAACsB,QAAQ,CAAC,CAAC,CACtB;UACH;QACA,KAAK5B,aAAa;UAAE;YAClB,OAAO,CACLN,MAAM,CAAC0D,UAAU,CAAC3D,QAAQ,CAAC0B,KAAK,CAACS,QAAQ,CAAC,EAAE;cAC1CyB,SAAS,EAAEL,OAAO;cAClBM,SAAS,EAAE3D,MAAM,CAAC4D,KAAK,CAAC;gBAAEC,MAAM,EAAEN,MAAM;gBAAEO,OAAO,EAAER;cAAS,CAAE;aAC/D,CAAC,EACF3C,SAAS,CAAC,CAAC,GAAGY,KAAK,CAACX,eAAe,EAAEqB,QAAQ,CAAC,CAAC,CAChD;UACH;QACA,KAAK3B,cAAc;UAAE;YACnB,OAAO,CAACP,MAAM,CAACmC,OAAO,CAACmB,OAAO,CAAC9B,KAAK,CAACT,KAAK,CAAC,CAAC,EAAES,KAAK,CAAC;UACtD;QACA,KAAKhB,aAAa;UAAE;YAClB,OAAO,CAACR,MAAM,CAACmC,OAAO,CAACqB,MAAM,CAAChC,KAAK,CAACP,IAAI,CAAC,CAAC,EAAEO,KAAK,CAAC;UACpD;MACF;IACF,CAAC,CAAC,CACH,CAAC;EACN;;AAGF;AACA,OAAO,MAAMiB,IAAI,GAAGA,CAAA,KAGlBtC,IAAI,CACFJ,QAAQ,CAAC0C,IAAI,EAAQ,EACrBzC,MAAM,CAACwC,OAAO,CAAEN,QAAQ,IAAK9B,GAAG,CAACqC,IAAI,CAAChC,UAAU,CAACyB,QAAQ,CAA2B,CAAC,CAAC,EACtFlC,MAAM,CAACmD,GAAG,CAAEhC,GAAG,IAAK,IAAID,4BAA4B,CAACC,GAAG,CAAC,CAAC,CAC3D", "ignoreList": []}