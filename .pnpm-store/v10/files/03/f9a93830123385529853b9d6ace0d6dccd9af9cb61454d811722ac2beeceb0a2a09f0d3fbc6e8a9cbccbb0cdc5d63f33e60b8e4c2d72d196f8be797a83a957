{"version": 3, "file": "TPubSub.js", "names": ["internal", "_interopRequireWildcard", "require", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TPubSubTypeId", "exports", "await<PERSON><PERSON><PERSON>down", "bounded", "capacity", "dropping", "isEmpty", "isFull", "shutdown", "isShutdown", "publish", "publishAll", "size", "sliding", "subscribe", "subscribeScoped", "unbounded"], "sources": ["../../src/TPubSub.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAKA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAqD,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAH,wBAAAG,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAQrD;;;;AAIO,MAAMW,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAkBvB,QAAQ,CAACuB,aAAa;AAsClE;;;;;;;;AAQO,MAAME,aAAa,GAAAD,OAAA,CAAAC,aAAA,GAA2CzB,QAAQ,CAACyB,aAAa;AAE3F;;;;;;;;AAQO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,GAA0D1B,QAAQ,CAAC0B,OAAO;AAE9F;;;;;;AAMO,MAAMC,QAAQ,GAAAH,OAAA,CAAAG,QAAA,GAAoC3B,QAAQ,CAAC2B,QAAQ;AAE1E;;;;;;;AAOO,MAAMC,QAAQ,GAAAJ,OAAA,CAAAI,QAAA,GAA0D5B,QAAQ,CAAC4B,QAAQ;AAEhG;;;;;;AAMO,MAAMC,OAAO,GAAAL,OAAA,CAAAK,OAAA,GAA8C7B,QAAQ,CAAC6B,OAAO;AAElF;;;;;;;AAOO,MAAMC,MAAM,GAAAN,OAAA,CAAAM,MAAA,GAA8C9B,QAAQ,CAAC8B,MAAM;AAEhF;;;;;;;AAOO,MAAMC,QAAQ,GAAAP,OAAA,CAAAO,QAAA,GAA2C/B,QAAQ,CAAC+B,QAAQ;AAEjF;;;;;;AAMO,MAAMC,UAAU,GAAAR,OAAA,CAAAQ,UAAA,GAA8ChC,QAAQ,CAACgC,UAAU;AAExF;;;;;;;AAOO,MAAMC,OAAO,GAAAT,OAAA,CAAAS,OAAA,GAiBhBjC,QAAQ,CAACiC,OAAO;AAEpB;;;;;;;AAOO,MAAMC,UAAU,GAAAV,OAAA,CAAAU,UAAA,GAiBnBlC,QAAQ,CAACkC,UAAU;AAEvB;;;;;;;;AAQO,MAAMC,IAAI,GAAAX,OAAA,CAAAW,IAAA,GAA6CnC,QAAQ,CAACmC,IAAI;AAE3E;;;;;;;;;AASO,MAAMC,OAAO,GAAAZ,OAAA,CAAAY,OAAA,GAA0DpC,QAAQ,CAACoC,OAAO;AAE9F;;;;;;;;;AASO,MAAMC,SAAS,GAAAb,OAAA,CAAAa,SAAA,GAAyDrC,QAAQ,CAACqC,SAAS;AAEjG;;;;;;;;AAQO,MAAMC,eAAe,GAAAd,OAAA,CAAAc,eAAA,GAC1BtC,QAAQ,CAACsC,eAAe;AAE1B;;;;;;AAMO,MAAMC,SAAS,GAAAf,OAAA,CAAAe,SAAA,GAAiCvC,QAAQ,CAACuC,SAAS", "ignoreList": []}