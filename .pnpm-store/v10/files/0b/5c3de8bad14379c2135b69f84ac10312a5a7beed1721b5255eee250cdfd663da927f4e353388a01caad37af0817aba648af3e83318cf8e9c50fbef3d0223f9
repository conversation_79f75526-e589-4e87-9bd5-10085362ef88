"use strict";
var import_chunk_H4NI2RIK = require("../../chunk-H4NI2RIK.js");
var import_chunk_4VNS5WPM = require("../../chunk-4VNS5WPM.js");
var originalEnv = { ...process.env };
var originalStdinisTTY = process.stdin.isTTY;
describe("isCi", () => {
  beforeEach(() => {
    process.env = originalEnv;
    process.stdin.isTTY = originalStdinisTTY;
  });
  afterAll(() => {
    process.env = originalEnv;
    process.stdin.isTTY = originalStdinisTTY;
  });
  describe("in non TTY environment", () => {
    beforeEach(() => {
      delete process.env.GITHUB_ACTIONS;
      delete process.env.CI;
      process.stdin.isTTY = false;
    });
    test("with undefined env vars, isCi should be false", () => {
      expect((0, import_chunk_H4NI2RIK.isCi)()).toBe(false);
    });
  });
  describe("in TTY environment", () => {
    beforeEach(() => {
      delete process.env.GITHUB_ACTIONS;
      delete process.env.CI;
      process.stdin.isTTY = true;
    });
    test("with CI env var, isCi should be true", () => {
      process.env.CI = "true";
      expect((0, import_chunk_H4NI2RIK.isCi)()).toBe(true);
    });
    test("with GitHub Actions env var, isCi should be true", () => {
      process.env.GITHUB_ACTIONS = "true";
      expect((0, import_chunk_H4NI2RIK.isCi)()).toBe(true);
    });
    test("with undefined env vars, isCi should be false", () => {
      expect((0, import_chunk_H4NI2RIK.isCi)()).toBe(false);
    });
  });
});
