"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.updateSomeEffect = exports.updateSomeAndGetEffect = exports.updateSomeAndGet = exports.updateSome = exports.updateEffect = exports.updateAndGetEffect = exports.updateAndGet = exports.update = exports.unsafeMake = exports.setAndGet = exports.set = exports.modifySomeEffect = exports.modifySome = exports.modifyEffect = exports.modify = exports.make = exports.getAndUpdateSomeEffect = exports.getAndUpdateSome = exports.getAndUpdateEffect = exports.getAndUpdate = exports.getAndSet = exports.get = exports.SynchronizedRefTypeId = void 0;
var circular = _interopRequireWildcard(require("./internal/effect/circular.js"));
var ref = _interopRequireWildcard(require("./internal/ref.js"));
var internal = _interopRequireWildcard(require("./internal/synchronizedRef.js"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
/**
 * @since 2.0.0
 * @category symbols
 */
const SynchronizedRefTypeId = exports.SynchronizedRefTypeId = circular.SynchronizedTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = circular.makeSynchronized;
/**
 * @since 2.0.0
 * @category getters
 */
const get = exports.get = ref.get;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndSet = exports.getAndSet = ref.getAndSet;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdate = exports.getAndUpdate = ref.getAndUpdate;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdateEffect = exports.getAndUpdateEffect = internal.getAndUpdateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdateSome = exports.getAndUpdateSome = ref.getAndUpdateSome;
/**
 * @since 2.0.0
 * @category utils
 */
const getAndUpdateSomeEffect = exports.getAndUpdateSomeEffect = internal.getAndUpdateSomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const modify = exports.modify = internal.modify;
/**
 * @since 2.0.0
 * @category utils
 */
const modifyEffect = exports.modifyEffect = internal.modifyEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const modifySome = exports.modifySome = ref.modifySome;
/**
 * @since 2.0.0
 * @category utils
 */
const modifySomeEffect = exports.modifySomeEffect = internal.modifySomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const set = exports.set = ref.set;
/**
 * @since 2.0.0
 * @category utils
 */
const setAndGet = exports.setAndGet = ref.setAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
const update = exports.update = ref.update;
/**
 * @since 2.0.0
 * @category utils
 */
const updateEffect = exports.updateEffect = internal.updateEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const updateAndGet = exports.updateAndGet = ref.updateAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
const updateAndGetEffect = exports.updateAndGetEffect = internal.updateAndGetEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSome = exports.updateSome = ref.updateSome;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSomeEffect = exports.updateSomeEffect = internal.updateSomeEffect;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSomeAndGet = exports.updateSomeAndGet = ref.updateSomeAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
const updateSomeAndGetEffect = exports.updateSomeAndGetEffect = circular.updateSomeAndGetEffectSynchronized;
/**
 * @since 2.0.0
 * @category unsafe
 */
const unsafeMake = exports.unsafeMake = circular.unsafeMakeSynchronized;
//# sourceMappingURL=SynchronizedRef.js.map