{"version": 3, "file": "FiberStatus.js", "names": ["internal", "FiberStatusTypeId", "done", "running", "suspended", "isFiberStatus", "isDone", "isRunning", "isSuspended"], "sources": ["../../src/FiberStatus.ts"], "sourcesContent": [null], "mappings": "AAKA,OAAO,KAAKA,QAAQ,MAAM,2BAA2B;AAGrD;;;;AAIA,OAAO,MAAMC,iBAAiB,GAAkBD,QAAQ,CAACC,iBAAiB;AA4C1E;;;;AAIA,OAAO,MAAMC,IAAI,GAAgBF,QAAQ,CAACE,IAAI;AAE9C;;;;AAIA,OAAO,MAAMC,OAAO,GAA6DH,QAAQ,CAACG,OAAO;AAEjG;;;;AAIA,OAAO,MAAMC,SAAS,GACpBJ,QAAQ,CAACI,SAAS;AAEpB;;;;;;AAMA,OAAO,MAAMC,aAAa,GAAqCL,QAAQ,CAACK,aAAa;AAErF;;;;;;AAMA,OAAO,MAAMC,MAAM,GAAwCN,QAAQ,CAACM,MAAM;AAE1E;;;;;;;AAOA,OAAO,MAAMC,SAAS,GAA2CP,QAAQ,CAACO,SAAS;AAEnF;;;;;;;AAOA,OAAO,MAAMC,WAAW,GAA6CR,QAAQ,CAACQ,WAAW", "ignoreList": []}