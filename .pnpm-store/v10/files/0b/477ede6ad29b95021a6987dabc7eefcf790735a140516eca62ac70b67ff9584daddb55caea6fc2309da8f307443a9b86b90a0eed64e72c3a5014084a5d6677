"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.PullRight = exports.PullLeft = exports.PullBoth = exports.OP_PULL_RIGHT = exports.OP_PULL_LEFT = exports.OP_PULL_BOTH = exports.OP_DRAIN_RIGHT = exports.OP_DRAIN_LEFT = exports.DrainRight = exports.DrainLeft = void 0;
/** @internal */
const OP_DRAIN_LEFT = exports.OP_DRAIN_LEFT = "DrainLeft";
/** @internal */
const OP_DRAIN_RIGHT = exports.OP_DRAIN_RIGHT = "DrainRight";
/** @internal */
const OP_PULL_BOTH = exports.OP_PULL_BOTH = "PullBoth";
/** @internal */
const OP_PULL_LEFT = exports.OP_PULL_LEFT = "PullLeft";
/** @internal */
const OP_PULL_RIGHT = exports.OP_PULL_RIGHT = "PullRight";
/** @internal */
const DrainLeft = exports.DrainLeft = {
  _tag: OP_DRAIN_LEFT
};
/** @internal */
const DrainRight = exports.DrainRight = {
  _tag: OP_DRAIN_RIGHT
};
/** @internal */
const PullBoth = exports.PullBoth = {
  _tag: OP_PULL_BOTH
};
/** @internal */
const PullLeft = rightChunk => ({
  _tag: OP_PULL_LEFT,
  rightChunk
});
/** @internal */
exports.PullLeft = PullLeft;
const PullRight = leftChunk => ({
  _tag: OP_PULL_RIGHT,
  leftChunk
});
exports.PullRight = PullRight;
//# sourceMappingURL=zipAllState.js.map