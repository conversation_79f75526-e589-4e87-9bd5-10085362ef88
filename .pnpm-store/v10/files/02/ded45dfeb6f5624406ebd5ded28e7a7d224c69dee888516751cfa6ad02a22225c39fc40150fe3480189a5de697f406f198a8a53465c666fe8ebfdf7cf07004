{"version": 3, "file": "handoffSignal.js", "names": ["OP_EMIT", "OP_HALT", "OP_END", "emit", "elements", "_tag", "halt", "cause", "end", "reason"], "sources": ["../../../../src/internal/stream/handoffSignal.ts"], "sourcesContent": [null], "mappings": "AAOA;AACA,OAAO,MAAMA,OAAO,GAAG,MAAe;AAKtC;AACA,OAAO,MAAMC,OAAO,GAAG,MAAe;AAKtC;AACA,OAAO,MAAMC,MAAM,GAAG,KAAc;AAsBpC;AACA,OAAO,MAAMC,IAAI,GAAOC,QAAwB,KAAwB;EACtEC,IAAI,EAAEL,OAAO;EACbI;CACD,CAAC;AAEF;AACA,OAAO,MAAME,IAAI,GAAOC,KAAqB,KAA+B;EAC1EF,IAAI,EAAEJ,OAAO;EACbM;CACD,CAAC;AAEF;AACA,OAAO,MAAMC,GAAG,GAAIC,MAAmC,KAA4B;EACjFJ,IAAI,EAAEH,MAAM;EACZO;CACD,CAAC", "ignoreList": []}