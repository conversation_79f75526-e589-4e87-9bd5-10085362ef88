{"version": 3, "file": "bitwise.js", "names": ["MASK", "popcount", "x", "hashFragment", "shift", "h", "toBitmap", "fromBitmap", "bitmap", "bit"], "sources": ["../../../../src/internal/hashMap/bitwise.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,IAAI,QAAQ,aAAa;AAElC;;;;;;;AAOA,OAAM,SAAUC,QAAQA,CAACC,CAAS;EAChCA,CAAC,IAAKA,CAAC,IAAI,CAAC,GAAI,UAAU;EAC1BA,CAAC,GAAG,CAACA,CAAC,GAAG,UAAU,KAAMA,CAAC,IAAI,CAAC,GAAI,UAAU,CAAC;EAC9CA,CAAC,GAAIA,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,GAAI,UAAU;EAC/BA,CAAC,IAAIA,CAAC,IAAI,CAAC;EACXA,CAAC,IAAIA,CAAC,IAAI,EAAE;EACZ,OAAOA,CAAC,GAAG,IAAI;AACjB;AAEA;AACA,OAAM,SAAUC,YAAYA,CAACC,KAAa,EAAEC,CAAS;EACnD,OAAQA,CAAC,KAAKD,KAAK,GAAIJ,IAAI;AAC7B;AAEA;AACA,OAAM,SAAUM,QAAQA,CAACJ,CAAS;EAChC,OAAO,CAAC,IAAIA,CAAC;AACf;AAEA;AACA,OAAM,SAAUK,UAAUA,CAACC,MAAc,EAAEC,GAAW;EACpD,OAAOR,QAAQ,CAACO,MAAM,GAAIC,GAAG,GAAG,CAAE,CAAC;AACrC", "ignoreList": []}