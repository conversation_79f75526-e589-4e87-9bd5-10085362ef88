{"version": 3, "file": "Scope.js", "names": ["core", "fiberRuntime", "ScopeTypeId", "CloseableScopeTypeId", "<PERSON><PERSON>", "scopeTag", "addFinalizer", "scopeAddFinalizer", "addFinalizerExit", "scopeAddFinalizerExit", "close", "scopeClose", "extend", "scopeExtend", "fork", "scopeFork", "use", "scopeUse", "make", "scopeMake"], "sources": ["../../src/Scope.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAQA,OAAO,KAAKA,IAAI,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,YAAY,MAAM,4BAA4B;AAG1D;;;;;;AAMA,OAAO,MAAMC,WAAW,GAAkBF,IAAI,CAACE,WAAW;AAU1D;;;;;;AAMA,OAAO,MAAMC,oBAAoB,GAAkBH,IAAI,CAACG,oBAAoB;AAsD5E;;;;;;AAMA,OAAO,MAAMC,KAAK,GAA8BH,YAAY,CAACI,QAAQ;AAsBrE;;;;;;;;;AASA,OAAO,MAAMC,YAAY,GAGEN,IAAI,CAACO,iBAAiB;AAEjD;;;;;;;;;AASA,OAAO,MAAMC,gBAAgB,GAC3BR,IAAI,CAACS,qBAAqB;AAE5B;;;;;;;AAOA,OAAO,MAAMC,KAAK,GAAqFV,IAAI,CAACW,UAAU;AAEtH;;;;;;;;;AASA,OAAO,MAAMC,MAAM,GAqBfX,YAAY,CAACY,WAAW;AAE5B;;;;;;;AAOA,OAAO,MAAMC,IAAI,GAGoBd,IAAI,CAACe,SAAS;AAEnD;;;;;;;;;AASA,OAAO,MAAMC,GAAG,GAqBZf,YAAY,CAACgB,QAAQ;AAEzB;;;;;;;;AAQA,OAAO,MAAMC,IAAI,GAEoBjB,YAAY,CAACkB,SAAS", "ignoreList": []}