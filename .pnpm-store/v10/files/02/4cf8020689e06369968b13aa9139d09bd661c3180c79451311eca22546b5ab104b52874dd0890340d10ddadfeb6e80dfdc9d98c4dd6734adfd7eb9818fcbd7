{"version": 3, "file": "RuntimeFlags.js", "names": ["circular", "_interopRequireWildcard", "require", "internal", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "None", "exports", "Interruption", "OpSupervision", "RuntimeMetrics", "WindDown", "CooperativeYielding", "cooperativeYielding", "diff", "differ", "disable", "disableAll", "disableCooperativeYielding", "disableInterruption", "disableOpSupervision", "disableRuntimeMetrics", "disableWindDown", "enable", "enableAll", "enableCooperativeYielding", "enableInterruption", "enableOpSupervision", "enableRuntimeMetrics", "enableWindDown", "interruptible", "interruption", "isEnabled", "isDisabled", "make", "none", "opSupervision", "patch", "render", "runtimeMetrics", "toSet", "windDown"], "sources": ["../../src/RuntimeFlags.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAKA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAsD,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AANtD;;;;AAiCA;;;;;;AAMO,MAAMW,IAAI,GAAAC,OAAA,CAAAD,IAAA,GAAgBrB,QAAQ,CAACqB,IAAI;AAE9C;;;;;;;AAOO,MAAME,YAAY,GAAAD,OAAA,CAAAC,YAAA,GAAgBvB,QAAQ,CAACuB,YAAY;AAE9D;;;;;;;;;AASO,MAAMC,aAAa,GAAAF,OAAA,CAAAE,aAAA,GAAgBxB,QAAQ,CAACwB,aAAa;AAEhE;;;;;;;;;;AAUO,MAAMC,cAAc,GAAAH,OAAA,CAAAG,cAAA,GAAgBzB,QAAQ,CAACyB,cAAc;AAElE;;;;;;;;;AASO,MAAMC,QAAQ,GAAAJ,OAAA,CAAAI,QAAA,GAAgB1B,QAAQ,CAAC0B,QAAQ;AAEtD;;;;;;;AAOO,MAAMC,mBAAmB,GAAAL,OAAA,CAAAK,mBAAA,GAAgB3B,QAAQ,CAAC2B,mBAAmB;AAE5E;;;;;;;AAOO,MAAMC,mBAAmB,GAAAN,OAAA,CAAAM,mBAAA,GAAoC5B,QAAQ,CAAC4B,mBAAmB;AAEhG;;;;;;;AAOO,MAAMC,IAAI,GAAAP,OAAA,CAAAO,IAAA,GAiBb7B,QAAQ,CAAC6B,IAAI;AAEjB;;;;;;AAMO,MAAMC,MAAM,GAAAR,OAAA,CAAAQ,MAAA,GAAqE9B,QAAQ,CAAC8B,MAAM;AAEvG;;;;;;AAMO,MAAMC,OAAO,GAAAT,OAAA,CAAAS,OAAA,GAehB/B,QAAQ,CAAC+B,OAAO;AAEpB;;;;;;AAMO,MAAMC,UAAU,GAAAV,OAAA,CAAAU,UAAA,GAenBhC,QAAQ,CAACgC,UAAU;AAEvB;;;;AAIO,MAAMC,0BAA0B,GAAAX,OAAA,CAAAW,0BAAA,GAAuBpC,QAAQ,CAACoC,0BAA0B;AAEjG;;;;AAIO,MAAMC,mBAAmB,GAAAZ,OAAA,CAAAY,mBAAA,GAAuBrC,QAAQ,CAACqC,mBAAmB;AAEnF;;;;AAIO,MAAMC,oBAAoB,GAAAb,OAAA,CAAAa,oBAAA,GAAuBtC,QAAQ,CAACsC,oBAAoB;AAErF;;;;AAIO,MAAMC,qBAAqB,GAAAd,OAAA,CAAAc,qBAAA,GAAuBvC,QAAQ,CAACuC,qBAAqB;AAEvF;;;;AAIO,MAAMC,eAAe,GAAAf,OAAA,CAAAe,eAAA,GAAuBxC,QAAQ,CAACwC,eAAe;AAE3E;;;;;;AAMO,MAAMC,MAAM,GAAAhB,OAAA,CAAAgB,MAAA,GAeftC,QAAQ,CAACsC,MAAM;AAEnB;;;;;;AAMO,MAAMC,SAAS,GAAAjB,OAAA,CAAAiB,SAAA,GAelBvC,QAAQ,CAACuC,SAAS;AAEtB;;;;AAIO,MAAMC,yBAAyB,GAAAlB,OAAA,CAAAkB,yBAAA,GAAuB3C,QAAQ,CAAC2C,yBAAyB;AAE/F;;;;AAIO,MAAMC,kBAAkB,GAAAnB,OAAA,CAAAmB,kBAAA,GAAuB5C,QAAQ,CAAC4C,kBAAkB;AAEjF;;;;AAIO,MAAMC,mBAAmB,GAAApB,OAAA,CAAAoB,mBAAA,GAAuB7C,QAAQ,CAAC6C,mBAAmB;AAEnF;;;;AAIO,MAAMC,oBAAoB,GAAArB,OAAA,CAAAqB,oBAAA,GAAuB9C,QAAQ,CAAC8C,oBAAoB;AAErF;;;;AAIO,MAAMC,cAAc,GAAAtB,OAAA,CAAAsB,cAAA,GAAuB/C,QAAQ,CAAC+C,cAAc;AAEzE;;;;;;;;;;;AAWO,MAAMC,aAAa,GAAAvB,OAAA,CAAAuB,aAAA,GAAoC7C,QAAQ,CAAC6C,aAAa;AAEpF;;;;;;;AAOO,MAAMC,YAAY,GAAAxB,OAAA,CAAAwB,YAAA,GAAoC9C,QAAQ,CAAC8C,YAAY;AAElF;;;;;;AAMO,MAAMC,SAAS,GAAAzB,OAAA,CAAAyB,SAAA,GAelB/C,QAAQ,CAAC+C,SAAS;AAEtB;;;;;;AAMO,MAAMC,UAAU,GAAA1B,OAAA,CAAA0B,UAAA,GAenBhD,QAAQ,CAACgD,UAAU;AAEvB;;;;AAIO,MAAMC,IAAI,GAAA3B,OAAA,CAAA2B,IAAA,GAA2DjD,QAAQ,CAACiD,IAAI;AAEzF;;;;AAIO,MAAMC,IAAI,GAAA5B,OAAA,CAAA4B,IAAA,GAAiBlD,QAAQ,CAACkD,IAAI;AAE/C;;;;;;;AAOO,MAAMC,aAAa,GAAA7B,OAAA,CAAA6B,aAAA,GAAoCnD,QAAQ,CAACmD,aAAa;AAEpF;;;;;;;AAOO,MAAMC,KAAK,GAAA9B,OAAA,CAAA8B,KAAA,GAiBdpD,QAAQ,CAACoD,KAAK;AAElB;;;;;;AAMO,MAAMC,MAAM,GAAA/B,OAAA,CAAA+B,MAAA,GAAmCrD,QAAQ,CAACqD,MAAM;AAErE;;;;;;;AAOO,MAAMC,cAAc,GAAAhC,OAAA,CAAAgC,cAAA,GAAoCtD,QAAQ,CAACsD,cAAc;AAEtF;;;;;;AAMO,MAAMC,KAAK,GAAAjC,OAAA,CAAAiC,KAAA,GAAqDvD,QAAQ,CAACuD,KAAK;AAErF;;;;;;;AAOO,MAAMC,QAAQ,GAAAlC,OAAA,CAAAkC,QAAA,GAAoCxD,QAAQ,CAACwD,QAAQ", "ignoreList": []}