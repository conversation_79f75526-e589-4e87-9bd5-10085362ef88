{"version": 3, "file": "Request.js", "names": ["RequestBlock_", "cache", "core", "fiberRuntime", "internal", "RequestTypeId", "isRequest", "of", "tagged", "Class", "TaggedClass", "complete", "interruptWhenPossible", "completeEffect", "fail", "failCause", "succeed", "makeCache", "options", "make", "lookup", "map", "deferred<PERSON><PERSON>", "handle", "listeners", "Listeners", "EntryTypeId", "Symbol", "for", "isEntry", "makeEntry"], "sources": ["../../src/Request.ts"], "sourcesContent": [null], "mappings": "AAUA,OAAO,KAAKA,aAAa,MAAM,+BAA+B;AAC9D,OAAO,KAAKC,KAAK,MAAM,qBAAqB;AAC5C,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,YAAY,MAAM,4BAA4B;AAC1D,OAAO,KAAKC,QAAQ,MAAM,uBAAuB;AAIjD;;;;AAIA,OAAO,MAAMC,aAAa,GAAkBD,QAAQ,CAACC,aAAa;AA2ElE;;;;;;AAMA,OAAO,MAAMC,SAAS,GAAmDF,QAAQ,CAACE,SAAS;AAE3F;;;;;;AAMA,OAAO,MAAMC,EAAE,GAA8DH,QAAQ,CAACG,EAAE;AAExF;;;;;;AAMA,OAAO,MAAMC,MAAM,GAEmBJ,QAAQ,CAACI,MAAM;AAErD;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,KAAK,GAG2BL,QAAQ,CAACK,KAAY;AAElE;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,WAAW,GAK8CN,QAAQ,CAACM,WAAkB;AAEjG;;;;;;AAMA,OAAO,MAAMC,QAAQ,GAejBP,QAAQ,CAACO,QAAQ;AAErB;;;;;;AAMA,OAAO,MAAMC,qBAAqB,GAe9BT,YAAY,CAACS,qBAAqB;AAEtC;;;;;;;;AAQA,OAAO,MAAMC,cAAc,GAwBvBT,QAAQ,CAACS,cAAc;AAE3B;;;;;;AAMA,OAAO,MAAMC,IAAI,GAebV,QAAQ,CAACU,IAAI;AAEjB;;;;;;AAMA,OAAO,MAAMC,SAAS,GAelBX,QAAQ,CAACW,SAAS;AAEtB;;;;;;AAMA,OAAO,MAAMC,OAAO,GAehBZ,QAAQ,CAACY,OAAO;AA2BpB;;;;AAIA,OAAO,MAAMC,SAAS,GACpBC,OAGC,IAEDjB,KAAK,CAACkB,IAAI,CAAC;EACT,GAAGD,OAAO;EACVE,MAAM,EAAEA,CAAA,KACNlB,IAAI,CAACmB,GAAG,CAACnB,IAAI,CAACoB,YAAY,EAAoB,EAAGC,MAAM,KAAM;IAAEC,SAAS,EAAE,IAAIpB,QAAQ,CAACqB,SAAS,EAAE;IAAEF;EAAM,CAAE,CAAC;CAChH,CAAC;AAEJ;;;;AAIA,OAAO,MAAMG,WAAW,gBAAkBC,MAAM,CAACC,GAAG,CAAC,2BAA2B,CAAC;AA+CjF;;;;AAIA,OAAO,MAAMC,OAAO,GAAG7B,aAAa,CAAC6B,OAAO;AAE5C;;;;AAIA,OAAO,MAAMC,SAAS,GAAG9B,aAAa,CAAC8B,SAAS", "ignoreList": []}