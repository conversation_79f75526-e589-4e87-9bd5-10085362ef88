{"name": "@typescript-eslint/scope-manager", "version": "8.32.1", "description": "TypeScript scope analyser for ESLint", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "package.json", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/scope-manager"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io/packages/scope-manager", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"build": "tsc -b tsconfig.build.json", "clean": "rimraf dist/ coverage/", "clean-fixtures": "rimraf -g \"./src/**/fixtures/**/snapshots\"", "format": "prettier --write \"./**/*.{ts,mts,cts,tsx,js,mjs,cjs,jsx,json,md,css}\" --ignore-path ../../.prettierignore", "generate-lib": "npx nx generate-lib repo", "lint": "npx nx lint", "test": "vitest --run --config=$INIT_CWD/vitest.config.mts", "check-types": "npx nx typecheck"}, "dependencies": {"@typescript-eslint/types": "8.32.1", "@typescript-eslint/visitor-keys": "8.32.1"}, "devDependencies": {"@typescript-eslint/typescript-estree": "8.32.1", "@vitest/coverage-v8": "^3.1.3", "@vitest/pretty-format": "^3.1.3", "glob": "*", "prettier": "^3.2.5", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}