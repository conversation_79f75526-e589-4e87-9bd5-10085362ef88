'use strict';

var assert = require('assert');
var tempfile = require('tempfile');
var makeDir = require('make-dir');
var path = require('path');
var fs = require('fs/promises');
var child_process = require('child_process');
var paths = require('env-paths');
var os = require('os');
var ms = require('ms');
require('concat-stream');
var http = require('http');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var assert__default = /*#__PURE__*/_interopDefaultLegacy(assert);
var tempfile__default = /*#__PURE__*/_interopDefaultLegacy(tempfile);
var makeDir__default = /*#__PURE__*/_interopDefaultLegacy(makeDir);
var path__default = /*#__PURE__*/_interopDefaultLegacy(path);
var fs__default = /*#__PURE__*/_interopDefaultLegacy(fs);
var paths__default = /*#__PURE__*/_interopDefaultLegacy(paths);
var os__default = /*#__PURE__*/_interopDefaultLegacy(os);
var ms__default = /*#__PURE__*/_interopDefaultLegacy(ms);
var http__default = /*#__PURE__*/_interopDefaultLegacy(http);

// U is the subset of T, not sure why
// this works or why _T is necessary










// valid default schema
const defaultSchema = {
  last_reminder: 0,
  cached_at: 0,
  version: '',
  cli_path: '',
  // User output
  output: {
    client_event_id: '',
    previous_client_event_id: '',
    product: '',
    cli_path_hash: '',
    local_timestamp: '',
    previous_version: '',
    current_version: '',
    current_release_date: 0,
    current_download_url: '',
    current_changelog_url: '',
    package: '',
    release_tag: '',
    install_command: '',
    project_website: '',
    outdated: false,
    alerts: [],
  },
};

// initialize the configuration
class Config {
  static async new(state, schema = defaultSchema) {
    await makeDir__default["default"](path__default["default"].dirname(state.cache_file));
    return new Config(state, schema)
  }

  constructor(  state,   defaultSchema) {this.state = state;this.defaultSchema = defaultSchema;}

  // check and return the cache if (matches version or hasn't expired)
  async checkCache(newState) {
    const now = newState.now();
    // fetch the data from the cache
    const cache = await this.all();

    if (!cache) {
      return { cache: undefined, stale: true }
    }
    // version has been upgraded or changed
    // TODO: define this behaviour more clearly.
    if (newState.version !== cache.version) {
      return { cache, stale: true }
    }
    // cache expired
    if (now - cache.cached_at > newState.cache_duration) {
      return { cache, stale: true }
    }
    return { cache, stale: false }
  }

  // set the configuration
  async set(update) {
    const existing = (await this.all()) || {};
    const schema = Object.assign(existing, update);
    // TODO: figure out how to type this
    for (let k in this.defaultSchema) {
      // @ts-ignore
      if (typeof schema[k] === 'undefined') {
        // @ts-ignore
        schema[k] = this.defaultSchema[k];
      }
    }
    await fs__default["default"].writeFile(this.state.cache_file, JSON.stringify(schema, null, '  '));
  }

  // get the entire schema
  async all() {
    try {
      const data = await fs__default["default"].readFile(this.state.cache_file, 'utf8');
      return JSON.parse(data)
    } catch (err) {
      return
    }
  }

  // get a value from the schema
  async get(key) {
    const schema = await this.all();
    if (typeof schema === 'undefined') {
      return
    }
    return schema[key]
  }

  // reset the configuration
  async reset() {
    await fs__default["default"].writeFile(this.state.cache_file, JSON.stringify(this.defaultSchema, null, '  '));
    return
  }

  // delete the configuration, ignoring any errors
  async delete() {
    try {
      await fs__default["default"].unlink(this.state.cache_file);
      return
    } catch (err) {
      return
    }
  }
}

const TELEMETRY_ENDPOINT_URL_PRODUCTION = 'https://checkpoint.prisma.io';

// JS-Checkpoint is a API-compatible JS client for a checkpoint server, like https://checkpoint.hashicorp.com/.

// Child path of the binary
// eval("__dirname") to make ncc happy
const childPath = path__default["default"].join(eval('__dirname'), 'child');

// actual implementation of the check() function
async function check(input) {
  // Create a cache file for this instance of the CLI path
  const defaultCache = getCacheFile(input.product, input.cli_path_hash || 'default');
  const ci = require('ci-info'); 

  // In order of preference
  // 1. `endpoint` from the input
  // 2. `PRISMA_TELEMETRY_ENDPOINT` env var
  // 3. Default
  const endpointUrl = input.endpoint || process.env.PRISMA_TELEMETRY_ENDPOINT || TELEMETRY_ENDPOINT_URL_PRODUCTION;

  // initialize the internal state
  const state = {
    product: input.product,
    version: input.version,
    cli_install_type: input.cli_install_type || '',
    information: input.information || '',
    local_timestamp: input.local_timestamp || rfc3339(new Date()),
    project_hash: input.project_hash,
    cli_path: input.cli_path || '',
    cli_path_hash: input.cli_path_hash || '',
    endpoint: endpointUrl,
    disable: typeof input.disable === 'undefined' ? false : input.disable,
    arch: input.arch || os__default["default"].arch(),
    os: input.os || os__default["default"].platform(),
    node_version: input.node_version || process.version,
    ci: typeof input.ci !== 'undefined' ? input.ci : ci.isCI,
    ci_name: typeof input.ci_name !== 'undefined' ? input.ci_name || '' : ci.name || '',
    command: input.command || '',
    schema_providers: input.schema_providers || [],
    schema_preview_features: input.schema_preview_features || [],
    schema_generators_providers: input.schema_generators_providers || [],
    cache_file: input.cache_file || defaultCache,
    cache_duration: typeof input.cache_duration === 'undefined' ? ms__default["default"]('12h') : input.cache_duration,
    remind_duration: typeof input.remind_duration === 'undefined' ? ms__default["default"]('48h') : input.remind_duration,
    force: typeof input.force === 'undefined' ? false : input.force,
    timeout: getTimeout(input.timeout),
    unref: typeof input.unref === 'undefined' ? true : input.unref,
    child_path: input.child_path || childPath,
    now: () => Date.now(),
    client_event_id: input.client_event_id || '',
    previous_client_event_id: input.previous_client_event_id || '',
    check_if_update_available: false,
  };

  // the CHECKPOINT_DISABLE environment variable will disable checkpoint from
  // checking for a new version or for alerts
  if ((process.env['CHECKPOINT_DISABLE'] || state.disable) && !state.force) {
    return {
      status: 'disabled',
    }
  }

  // make the cache file without if we haven't already
  const config = await Config.new(state);
  // check if we've already cached the response
  const cacheResponse = await config.checkCache(state);

  // if the cache is stale (can be expired, or uses a different version):
  // send an additional `check_if_update_needed` field to the telemetry service to get a new response for versions
  state.check_if_update_available = cacheResponse.stale === true || !cacheResponse.cache;

  // Spawn the child to send telemetry request
  const child = spawn(state);

  if (state.unref) {
    child.unref();
    // Closes the IPC channel between parent and child
    child.disconnect();
  }

  // If the cache is stale or does not exist, wait for it to be invalidated
  if (cacheResponse.stale === true || !cacheResponse.cache) {
    return {
      status: 'waiting',
      data: child,
    }
  }

  // instead of resetting the whole cache, only update where `state` has non-nullish values
  for (const key of Object.keys(state)) {
    if (state[key]) {
      await config.set({
        [key]: state[key],
      });
    }
  }

  // lastly, check if we've recently informed the user
  const userReminded = state.now() - cacheResponse.cache.last_reminder < state.remind_duration;

  if (userReminded) {
    // User has been reminded. Don't inform them right now
    return {
      status: 'reminded',
      data: cacheResponse.cache.output,
    }
  }

  // otherwise update the last_reminder and return the cache
  await config.set({
    last_reminder: state.now(),
  });

  return {
    status: 'ok',
    data: cacheResponse.cache.output,
  }
}

/**
 *
 * @param product The name of the product, e.g. 'prisma'
 * @param cacheIdentifier Identifier to differentiate different cache files for a product
 */
function getCacheFile(product, cacheIdentifier) {
  const dirs = paths__default["default"](`checkpoint`); // Get a user local storage path
  return path__default["default"].join(dirs.cache, `${product}-${cacheIdentifier}`)
}

// get the timeout from the input or environment variable
function getTimeout(inputTimeout) {
  if (typeof inputTimeout !== 'undefined') {
    return inputTimeout
  }
  // the CHECKPOINT_TIMEOUT for compatibility with go-checkpoint
  const timeoutString = process.env['CHECKPOINT_TIMEOUT'];
  if (typeof timeoutString === 'undefined') {
    return 5000
  }
  const timeout = parseInt(timeoutString, 10);
  if (isNaN(timeout)) {
    return 5000
  }
  return timeout
}

function getForkOpts(state) {
  if (state.unref === true) {
    return {
      detached: true,
      // When CHECKPOINT_DEBUG_STDOUT !== undefined, use 'inherit' to let the child print the debug output
      stdio: process.env.CHECKPOINT_DEBUG_STDOUT ? 'inherit' : 'ignore',
      env: process.env,
    }
  }

  return { detached: false, stdio: 'pipe', env: process.env }
}

// spawn a child
function spawn(state) {
  return child_process.fork(childPath, [JSON.stringify(state)], getForkOpts(state))
}

// Returns an rfc3339 compliant local date string with timezone.
// This function is unfortunately necessary because Date.toISOString() always returns the time in UTC.
// This function return an RFC3339 formatted string in the user's local time zone.
function rfc3339(d) {
  function pad(n) {
    return n < 10 ? '0' + n : n
  }

  function timezoneOffset(offset) {
    let sign;
    if (offset === 0) {
      return 'Z'
    }
    sign = offset > 0 ? '-' : '+';
    offset = Math.abs(offset);
    return sign + pad(Math.floor(offset / 60)) + ':' + pad(offset % 60)
  }

  return (
    d.getFullYear() +
    '-' +
    pad(d.getMonth() + 1) +
    '-' +
    pad(d.getDate()) +
    'T' +
    pad(d.getHours()) +
    ':' +
    pad(d.getMinutes()) +
    ':' +
    pad(d.getSeconds()) +
    timezoneOffset(d.getTimezoneOffset())
  )
}

async function serve(handler) {
  const server = await new Promise((resolve) => {
    const s = http__default["default"].createServer(handler);
    s.listen(0, '127.0.0.1', () => resolve(s));
  });

  const addr = server.address();
  if (!addr || typeof addr === 'string') {
    throw new Error('unable to start the server')
  }

  return {
    url: `http://${addr.address}:${addr.port}`,
    close: () => new Promise((res) => server.close(() => res())),
  }
}

const projects = ['project1', 'project2'];
const localCliPathHash = 'b2b2b2b2';

let server;
let calls = 0;

beforeAll(async () => {
  server = await serve(() => {
    calls++;
    // slow server
  });
});

afterAll(async () => {
  await server.close();
});

it('should return status = disabled', async () => {
  const cache_file = tempfile__default["default"]();
  const result = await check({
    cli_install_type: 'global',
    disable: true,
    cli_path_hash: localCliPathHash,
    cache_file: cache_file,
    cli_path: 'a/b/c',
    endpoint: server.url,
    product: 'prisma',
    project_hash: projects[0],
    version: '0.0.1',
  });
  assert__default["default"].strictEqual(calls, 0);
  assert__default["default"].strictEqual(result.status, 'disabled');
});
