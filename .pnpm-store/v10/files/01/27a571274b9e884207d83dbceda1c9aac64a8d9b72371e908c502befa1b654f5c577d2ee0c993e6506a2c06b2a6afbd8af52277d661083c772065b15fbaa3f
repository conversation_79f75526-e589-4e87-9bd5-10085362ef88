{"version": 3, "file": "resource.js", "names": ["identity", "pipe", "core", "effectable", "fiberRuntime", "schedule_", "scopedRef", "ResourceSymbolKey", "ResourceTypeId", "Symbol", "for", "resourceVariance", "_E", "_", "_A", "proto", "CommitPrototype", "commit", "get", "auto", "acquire", "policy", "tap", "manual", "acquireRelease", "refresh", "schedule_Effect", "interruptible", "forkDaemon", "interruptFiber", "flatMap", "context", "env", "fromAcquire", "exit", "map", "ref", "resource", "Object", "create", "provideContext", "self", "set", "exitSucceed"], "sources": ["../../../src/internal/resource.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,QAAQ,EAAEC,IAAI,QAAQ,gBAAgB;AAI/C,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AACjD,OAAO,KAAKC,SAAS,MAAM,eAAe;AAC1C,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAE3C;AACA,MAAMC,iBAAiB,GAAG,iBAAiB;AAE3C;AACA,OAAO,MAAMC,cAAc,gBAA4BC,MAAM,CAACC,GAAG,CAC/DH,iBAAiB,CACS;AAE5B,MAAMI,gBAAgB,GAAG;EACvB;EACAC,EAAE,EAAGC,CAAM,IAAKA,CAAC;EACjB;EACAC,EAAE,EAAGD,CAAM,IAAKA;CACjB;AAED;AACA,MAAME,KAAK,GAA0C;EACnD,GAAGZ,UAAU,CAACa,eAAe;EAC7BC,MAAMA,CAAA;IACJ,OAAOC,GAAG,CAAC,IAAI,CAAC;EAClB,CAAC;EACD,CAACV,cAAc,GAAGG;CACnB;AAED;AACA,OAAO,MAAMQ,IAAI,GAAGA,CAClBC,OAA+B,EAC/BC,MAA2C,KAE3CnB,IAAI,CAACoB,GAAG,CAACC,MAAM,CAACH,OAAO,CAAC,EAAGG,MAAM,IAC/BnB,YAAY,CAACoB,cAAc,CACzBvB,IAAI,CACFwB,OAAO,CAACF,MAAM,CAAC,EACflB,SAAS,CAACqB,eAAe,CAACL,MAAM,CAAC,EACjCnB,IAAI,CAACyB,aAAa,EAClBvB,YAAY,CAACwB,UAAU,CACxB,EACD1B,IAAI,CAAC2B,cAAc,CACpB,CAAC;AAEN;AACA,OAAO,MAAMN,MAAM,GACjBH,OAA+B,IAE/BlB,IAAI,CAAC4B,OAAO,CAAC5B,IAAI,CAAC6B,OAAO,EAAK,EAAGC,GAAG,IAClC/B,IAAI,CACFK,SAAS,CAAC2B,WAAW,CAAC/B,IAAI,CAACgC,IAAI,CAACd,OAAO,CAAC,CAAC,EACzClB,IAAI,CAACiC,GAAG,CAAEC,GAAG,IAAI;EACf,MAAMC,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAACxB,KAAK,CAAC;EACrCsB,QAAQ,CAAC/B,SAAS,GAAG8B,GAAG;EACxBC,QAAQ,CAACjB,OAAO,GAAGlB,IAAI,CAACsC,cAAc,CAACpB,OAAO,EAAEY,GAAG,CAAC;EACpD,OAAOK,QAAQ;AACjB,CAAC,CAAC,CACH,CAAC;AAEN;AACA,OAAO,MAAMnB,GAAG,GAAUuB,IAA6B,IACrDvC,IAAI,CAAC4B,OAAO,CAACxB,SAAS,CAACY,GAAG,CAACuB,IAAI,CAACnC,SAAS,CAAC,EAAEN,QAAQ,CAAC;AAEvD;AACA,OAAO,MAAMyB,OAAO,GAAUgB,IAA6B,IACzDnC,SAAS,CAACoC,GAAG,CACXD,IAAI,CAACnC,SAAS,EACdJ,IAAI,CAACiC,GAAG,CAACM,IAAI,CAACrB,OAAO,EAAElB,IAAI,CAACyC,WAAW,CAAC,CACzC", "ignoreList": []}