{"version": 3, "file": "key.js", "names": ["Arr", "Equal", "dual", "pipe", "Hash", "Option", "pipeArguments", "hasProperty", "metricKeyType", "metricLabel", "MetricKeySymbolKey", "MetricKeyTypeId", "Symbol", "for", "metricKeyVariance", "_Type", "_", "arrayEquivilence", "getEquivalence", "equals", "MetricKeyImpl", "name", "keyType", "description", "tags", "constructor", "_hash", "string", "combine", "hash", "array", "symbol", "u", "isMetricKey", "arguments", "counter", "options", "fromNullable", "frequency", "gauge", "histogram", "boundaries", "summary", "tagged", "self", "key", "value", "taggedWithL<PERSON><PERSON>", "make", "extraTags", "length", "union"], "sources": ["../../../../src/internal/metric/key.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,GAAG,MAAM,gBAAgB;AAErC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,SAASC,IAAI,EAAEC,IAAI,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,IAAI,MAAM,eAAe;AAKrC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AACzC,SAASC,aAAa,QAAQ,mBAAmB;AACjD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAO,KAAKC,aAAa,MAAM,cAAc;AAC7C,OAAO,KAAKC,WAAW,MAAM,YAAY;AAEzC;AACA,MAAMC,kBAAkB,GAAG,kBAAkB;AAE7C;AACA,OAAO,MAAMC,eAAe,gBAA8BC,MAAM,CAACC,GAAG,CAClEH,kBAAkB,CACU;AAE9B,MAAMI,iBAAiB,GAAG;EACxB;EACAC,KAAK,EAAGC,CAAQ,IAAKA;CACtB;AAED,MAAMC,gBAAgB,gBAAGjB,GAAG,CAACkB,cAAc,CAACjB,KAAK,CAACkB,MAAM,CAAC;AAEzD;AACA,MAAMC,aAAa;EAGNC,IAAA;EACAC,OAAA;EACAC,WAAA;EACAC,IAAA;EALF,CAACb,eAAe,IAAIG,iBAAiB;EAC9CW,YACWJ,IAAY,EACZC,OAAa,EACbC,WAAkC,EAClCC,IAAA,GAA+C,EAAE;IAHjD,KAAAH,IAAI,GAAJA,IAAI;IACJ,KAAAC,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IAEb,IAAI,CAACE,KAAK,GAAGvB,IAAI,CACfC,IAAI,CAACuB,MAAM,CAAC,IAAI,CAACN,IAAI,GAAG,IAAI,CAACE,WAAW,CAAC,EACzCnB,IAAI,CAACwB,OAAO,CAACxB,IAAI,CAACyB,IAAI,CAAC,IAAI,CAACP,OAAO,CAAC,CAAC,EACrClB,IAAI,CAACwB,OAAO,CAACxB,IAAI,CAAC0B,KAAK,CAAC,IAAI,CAACN,IAAI,CAAC,CAAC,CACpC;EACH;EACSE,KAAK;EACd,CAACtB,IAAI,CAAC2B,MAAM,IAAC;IACX,OAAO,IAAI,CAACL,KAAK;EACnB;EACA,CAACzB,KAAK,CAAC8B,MAAM,EAAEC,CAAU;IACvB,OAAOC,WAAW,CAACD,CAAC,CAAC,IACnB,IAAI,CAACX,IAAI,KAAKW,CAAC,CAACX,IAAI,IACpBpB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACG,OAAO,EAAEU,CAAC,CAACV,OAAO,CAAC,IACrCrB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACI,WAAW,EAAES,CAAC,CAACT,WAAW,CAAC,IAC7CN,gBAAgB,CAAC,IAAI,CAACO,IAAI,EAAEQ,CAAC,CAACR,IAAI,CAAC;EACvC;EACArB,IAAIA,CAAA;IACF,OAAOG,aAAa,CAAC,IAAI,EAAE4B,SAAS,CAAC;EACvC;;AAGF;AACA,OAAO,MAAMD,WAAW,GAAID,CAAU,IACpCzB,WAAW,CAACyB,CAAC,EAAErB,eAAe,CAAC;AAEjC;AACA,OAAO,MAAMwB,OAAO,GAWhBA,CAACd,IAAY,EAAEe,OAAO,KACxB,IAAIhB,aAAa,CACfC,IAAI,EACJb,aAAa,CAAC2B,OAAO,CAACC,OAAc,CAAC,EACrC/B,MAAM,CAACgC,YAAY,CAACD,OAAO,EAAEb,WAAW,CAAC,CAC1C;AAEH;AACA,OAAO,MAAMe,SAAS,GAAGA,CAACjB,IAAY,EAAEe,OAGvC,KACC,IAAIhB,aAAa,CAACC,IAAI,EAAEb,aAAa,CAAC8B,SAAS,CAACF,OAAO,CAAC,EAAE/B,MAAM,CAACgC,YAAY,CAACD,OAAO,EAAEb,WAAW,CAAC,CAAC;AAEtG;AACA,OAAO,MAAMgB,KAAK,GASdA,CAAClB,IAAI,EAAEe,OAAO,KAChB,IAAIhB,aAAa,CACfC,IAAI,EACJb,aAAa,CAAC+B,KAAK,CAACH,OAAc,CAAC,EACnC/B,MAAM,CAACgC,YAAY,CAACD,OAAO,EAAEb,WAAW,CAAC,CAC1C;AAEH;AACA,OAAO,MAAMiB,SAAS,GAAGA,CACvBnB,IAAY,EACZoB,UAA6C,EAC7ClB,WAAoB,KAEpB,IAAIH,aAAa,CACfC,IAAI,EACJb,aAAa,CAACgC,SAAS,CAACC,UAAU,CAAC,EACnCpC,MAAM,CAACgC,YAAY,CAACd,WAAW,CAAC,CACjC;AAEH;AACA,OAAO,MAAMmB,OAAO,GAClBN,OAOC,IAED,IAAIhB,aAAa,CACfgB,OAAO,CAACf,IAAI,EACZb,aAAa,CAACkC,OAAO,CAACN,OAAO,CAAC,EAC9B/B,MAAM,CAACgC,YAAY,CAACD,OAAO,CAACb,WAAW,CAAC,CACzC;AAEH;AACA,OAAO,MAAMoB,MAAM,gBAAGzC,IAAI,CAYxB,CAAC,EAAE,CAAC0C,IAAI,EAAEC,GAAG,EAAEC,KAAK,KAAKC,gBAAgB,CAACH,IAAI,EAAE,CAACnC,WAAW,CAACuC,IAAI,CAACH,GAAG,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;AAElF;AACA,OAAO,MAAMC,gBAAgB,gBAAG7C,IAAI,CAUlC,CAAC,EAAE,CAAC0C,IAAI,EAAEK,SAAS,KACnBA,SAAS,CAACC,MAAM,KAAK,CAAC,GAClBN,IAAI,GACJ,IAAIxB,aAAa,CAACwB,IAAI,CAACvB,IAAI,EAAEuB,IAAI,CAACtB,OAAO,EAAEsB,IAAI,CAACrB,WAAW,EAAEvB,GAAG,CAACmD,KAAK,CAACP,IAAI,CAACpB,IAAI,EAAEyB,SAAS,CAAC,CAAC,CAAC", "ignoreList": []}