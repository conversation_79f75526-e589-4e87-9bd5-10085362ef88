"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var chunk_U2ZCQFKU_exports = {};
__export(chunk_U2ZCQFKU_exports, {
  version: () => version
});
module.exports = __toCommonJS(chunk_U2ZCQFKU_exports);
var import_chunk_BCHR2OFA = require("./chunk-BCHR2OFA.js");
var packageJson = (0, import_chunk_BCHR2OFA.require_package)();
var version = packageJson.version;
