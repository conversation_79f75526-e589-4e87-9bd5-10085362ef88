import {
  credentialsFile,
  poll,
  printPpgInitOutput,
  requestOrThrow,
  successMessage
} from "./chunk-K4YS2DUC.js";

// src/Init.ts
import { confirm, input, select } from "@inquirer/prompts";
import internals from "@prisma/internals";
import dotenv from "dotenv";
import { Schema as Shape } from "effect";
import fs2 from "fs";
import { bold as bold2, dim, green, red as red2, yellow } from "kleur/colors";
import ora from "ora";
import path2 from "path";
import { match, P } from "ts-pattern";

// src/utils/client-output-path.ts
import fs from "fs";
import path from "path";
import { getTsconfig } from "get-tsconfig";
function determineClientOutputPath(schemaDir) {
  const sourceDir = getSourceDir();
  const outputPath = path.join(sourceDir, "generated", "prisma");
  const relativeOutputPath = path.relative(schemaDir, outputPath);
  return relativeOutputPath.replaceAll(path.sep, "/");
}
function getSourceDir() {
  const projectDir = process.cwd();
  const sourceRootFromTsConfig = getSourceDirFromTypeScriptConfig();
  if (sourceRootFromTsConfig) {
    return path.join(projectDir, sourceRootFromTsConfig);
  }
  for (const dir of ["src", "lib", "app"]) {
    const absoluteSourceDir = path.join(projectDir, dir);
    if (fs.existsSync(absoluteSourceDir)) {
      return absoluteSourceDir;
    }
  }
  return projectDir;
}
function getSourceDirFromTypeScriptConfig() {
  const tsconfig = getTsconfig();
  if (!tsconfig) {
    return void 0;
  }
  const { config } = tsconfig;
  return config.compilerOptions?.rootDir ?? config.compilerOptions?.baseUrl ?? config.compilerOptions?.rootDirs?.[0];
}

// src/platform/accelerate/regions.ts
var getRegionsOrThrow = async (input2) => {
  const { token } = input2;
  const { system } = await requestOrThrow({
    token,
    body: {
      query: (
        /* GraphQL */
        `
        query {
          system {
            accelerate {
              regions {
                id
                displayName
                ppgStatus
              }
            }
          }
        }
      `
      )
    }
  });
  return system.accelerate.regions;
};
var getPrismaPostgresRegionsOrThrow = async (input2) => {
  const regions = await getRegionsOrThrow(input2);
  const ppgRegions = regions.filter((_) => _.ppgStatus !== "unsupported").sort((a, b) => b.displayName.localeCompare(a.displayName));
  return ppgRegions;
};

// src/utils/print.ts
import { bgRed, bold, red } from "kleur/colors";
function printError(text) {
  return bold(bgRed(" ERROR ")) + " " + red(text);
}

// src/Init.ts
var PRISMA_DEV_DEFAULT_URL = "prisma+postgres://localhost:51213/?api_key=eyJkYXRhYmFzZVVybCI6InBvc3RncmVzOi8vcG9zdGdyZXM6cG9zdGdyZXNAbG9jYWxob3N0OjUxMjE0L3Bvc3RncmVzP2Nvbm5lY3Rpb25fbGltaXQ9MSZjb25uZWN0X3RpbWVvdXQ9MCZtYXhfaWRsZV9jb25uZWN0aW9uX2xpZmV0aW1lPTAmcG9vbF90aW1lb3V0PTAmc29ja2V0X3RpbWVvdXQ9MCZzc2xtb2RlPWRpc2FibGUiLCJzaGFkb3dEYXRhYmFzZVVybCI6InBvc3RncmVzOi8vcG9zdGdyZXM6cG9zdGdyZXNAbG9jYWxob3N0OjUxMjE1L3Bvc3RncmVzP2Nvbm5lY3Rpb25fbGltaXQ9MSZjb25uZWN0X3RpbWVvdXQ9MCZtYXhfaWRsZV9jb25uZWN0aW9uX2xpZmV0aW1lPTAmcG9vbF90aW1lb3V0PTAmc29ja2V0X3RpbWVvdXQ9MCZzc2xtb2RlPWRpc2FibGUifQ";
var { PRISMA_INIT_DEV_PREVIEW } = (typeof process !== "undefined" ? process.env : {}) || {};
var defaultSchema = (props) => {
  const {
    datasourceProvider = "postgresql",
    generatorProvider = defaultGeneratorProvider,
    previewFeatures = defaultPreviewFeatures,
    output = "../generated/prisma",
    withModel = false
  } = props ?? {};
  const aboutAccelerate = `
// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init
`;
  const isProviderCompatibleWithAccelerate = datasourceProvider !== "sqlite";
  let schema = `// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema
${isProviderCompatibleWithAccelerate ? aboutAccelerate : ""}
generator client {
  provider = "${generatorProvider}"
${previewFeatures.length > 0 ? `  previewFeatures = [${previewFeatures.map((feature) => `"${feature}"`).join(", ")}]
` : ""}  output   = "${output}"
}

datasource db {
  provider = "${datasourceProvider}"
  url      = env("DATABASE_URL")
}
`;
  if (withModel) {
    const defaultAttributes = `email String  @unique
  name  String?`;
    switch (datasourceProvider) {
      case "mongodb":
        schema += `
model User {
  id    String  @id @default(auto()) @map("_id") @db.ObjectId
  ${defaultAttributes}
}
`;
        break;
      case "cockroachdb":
        schema += `
model User {
  id    BigInt  @id @default(sequence())
  ${defaultAttributes}
}
`;
        break;
      default:
        schema += `
model User {
  id    Int     @id @default(autoincrement())
  ${defaultAttributes}
}
`;
    }
  }
  return schema;
};
var defaultEnv = (url = PRISMA_INIT_DEV_PREVIEW ? PRISMA_DEV_DEFAULT_URL : "postgresql://johndoe:randompassword@localhost:5432/mydb?schema=public", comments = true) => {
  let env = comments ? `# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

${PRISMA_INIT_DEV_PREVIEW && url === PRISMA_DEV_DEFAULT_URL ? `# The following \`prisma+postgres\` URL is similar to the URL produced by running a local Prisma Postgres 
# server with the \`prisma dev\` CLI command, when not choosing any non-default ports or settings. The API key, unlike the 
# one found in a remote Prisma Postgres URL, does not contain any sensitive information.

` : ""}` : "";
  env += `DATABASE_URL="${url}"`;
  return env;
};
var defaultPort = (datasourceProvider) => {
  switch (datasourceProvider) {
    case "mysql":
      return 3306;
    case "sqlserver":
      return 1433;
    case "mongodb":
      return 27017;
    case "postgresql":
      return 5432;
    case "cockroachdb":
      return 26257;
    case internals.PRISMA_POSTGRES_PROVIDER:
      return null;
  }
  return void 0;
};
var defaultURL = (datasourceProvider, port = defaultPort(datasourceProvider), schema = "public") => {
  switch (datasourceProvider) {
    case "postgresql":
      return `postgresql://johndoe:randompassword@localhost:${port}/mydb?schema=${schema}`;
    case "cockroachdb":
      return `postgresql://johndoe:randompassword@localhost:${port}/mydb?schema=${schema}`;
    case "mysql":
      return `mysql://johndoe:randompassword@localhost:${port}/mydb`;
    case "sqlserver":
      return `sqlserver://localhost:${port};database=mydb;user=SA;password=randompassword;`;
    case "mongodb":
      return `mongodb+srv://root:<EMAIL>/mydb?retryWrites=true&w=majority`;
    case "sqlite":
      return "file:./dev.db";
    default:
      return void 0;
  }
};
var defaultGitIgnore = () => {
  return `node_modules
# Keep environment variables out of version control
.env
`;
};
var defaultGeneratorProvider = "prisma-client-js";
var defaultPreviewFeatures = [];
var Init = class _Init {
  static new() {
    return new _Init();
  }
  static help = internals.format(`
  Set up a new Prisma project

  ${bold2("Usage")}

    ${dim("$")} prisma init [options]

  ${bold2("Options")}

             -h, --help   Display this help message
                   --db   Provisions a fully managed Prisma Postgres database on the Prisma Data Platform.
  --datasource-provider   Define the datasource provider to use: postgresql, mysql, sqlite, sqlserver, mongodb or cockroachdb
   --generator-provider   Define the generator provider to use. Default: \`prisma-client-js\`
      --preview-feature   Define a preview feature to use.
               --output   Define Prisma Client generator output path to use.
                  --url   Define a custom datasource url

  ${bold2("Flags")}

           --with-model   Add example model to created schema file

  ${bold2("Examples")}

  Set up a new ${PRISMA_INIT_DEV_PREVIEW ? "local Prisma Postgres project" : "Prisma project with PostgreSQL"} (default)
    ${dim("$")} prisma init

  Set up a new Prisma project and specify MySQL as the datasource provider to use
    ${dim("$")} prisma init --datasource-provider mysql

  Set up a new Prisma project and specify \`prisma-client-go\` as the generator provider to use
    ${dim("$")} prisma init --generator-provider prisma-client-go

  Set up a new Prisma project and specify \`x\` and \`y\` as the preview features to use
    ${dim("$")} prisma init --preview-feature x --preview-feature y

  Set up a new Prisma project and specify \`./generated-client\` as the output path to use
    ${dim("$")} prisma init --output ./generated-client

  Set up a new Prisma project and specify the url that will be used
    ${dim("$")} prisma init --url mysql://user:password@localhost:3306/mydb

  Set up a new Prisma project with an example model
    ${dim("$")} prisma init --with-model
  `);
  async parse(argv, _config) {
    const args = internals.arg(argv, {
      "--help": Boolean,
      "-h": "--help",
      "--url": String,
      "--datasource-provider": String,
      "--generator-provider": String,
      "--preview-feature": [String],
      "--output": String,
      "--with-model": Boolean,
      "--db": Boolean,
      "--region": String,
      "--name": String,
      "--non-interactive": Boolean,
      "--prompt": String,
      "--vibe": String
    });
    if (internals.isError(args) || args["--help"]) {
      return this.help();
    }
    internals.checkUnsupportedDataProxy({ cmd: "init", urls: [args["--url"]] });
    const outputDirName = args._[0];
    if (outputDirName) {
      throw Error("The init command does not take any argument.");
    }
    const { datasourceProvider, url } = await match(args).with(
      {
        "--datasource-provider": P.when(
          (datasourceProvider2) => Boolean(datasourceProvider2)
        )
      },
      (input2) => {
        const datasourceProvider2 = input2["--datasource-provider"].toLowerCase();
        assertDatasourceProvider(datasourceProvider2);
        const url2 = defaultURL(datasourceProvider2);
        return { datasourceProvider: datasourceProvider2, url: url2 };
      }
    ).with(
      {
        "--url": P.when((url2) => Boolean(url2))
      },
      async (input2) => {
        const url2 = input2["--url"];
        const canConnect = await internals.canConnectToDatabase(url2);
        if (canConnect !== true) {
          const { code, message } = canConnect;
          if (code !== "P1003") {
            if (code) {
              throw new Error(`${code}: ${message}`);
            } else {
              throw new Error(message);
            }
          }
        }
        const datasourceProvider2 = internals.protocolToConnectorType(`${url2.split(":")[0]}:`);
        return { datasourceProvider: datasourceProvider2, url: url2 };
      }
    ).otherwise(() => {
      return {
        datasourceProvider: "postgresql",
        url: void 0
      };
    });
    const generatorProvider = args["--generator-provider"];
    const previewFeatures = args["--preview-feature"];
    const output = args["--output"];
    const isPpgCommand = args["--db"] || datasourceProvider === internals.PRISMA_POSTGRES_PROVIDER || args["--prompt"] || args["--vibe"];
    let prismaPostgresDatabaseUrl;
    let workspaceId = ``;
    let projectId = ``;
    let environmentId = ``;
    const outputDir = process.cwd();
    const prismaFolder = path2.join(outputDir, "prisma");
    let generatedSchema;
    let generatedName;
    if (isPpgCommand) {
      const PlatformCommands = await import("./_-46NDLHTP.js");
      const credentials = await credentialsFile.load();
      if (internals.isError(credentials))
        throw credentials;
      if (!credentials) {
        if (args["--non-interactive"]) {
          return "Please authenticate before creating a Prisma Postgres project.";
        }
        console.log("This will create a project for you on console.prisma.io and requires you to be authenticated.");
        const authAnswer = await confirm({
          message: "Would you like to authenticate?"
        });
        if (!authAnswer) {
          return "Project creation aborted. You need to authenticate to use Prisma Postgres";
        }
        const authenticationResult = await PlatformCommands.loginOrSignup();
        console.log(`Successfully authenticated as ${bold2(authenticationResult.email)}.`);
      }
      if (args["--prompt"] || args["--vibe"]) {
        const prompt = args["--prompt"] || args["--vibe"] || "";
        const spinner2 = ora(`Generating a Prisma Schema based on your description ${bold2(prompt)} ...`).start();
        try {
          const serverResponseShape = Shape.Struct({
            generatedSchema: Shape.String,
            generatedName: Shape.String
          });
          ({ generatedSchema, generatedName } = Shape.decodeUnknownSync(serverResponseShape)(
            await (await fetch(`https://prisma-generate-server.prisma.workers.dev/`, {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({
                description: prompt
              })
            })).json()
          ));
        } catch (e) {
          spinner2.fail();
          throw e;
        }
        spinner2.succeed("Schema is ready");
      }
      console.log("Let's set up your Prisma Postgres database!");
      const platformToken = await PlatformCommands.getTokenOrThrow(args);
      const defaultWorkspace = await PlatformCommands.Workspace.getDefaultWorkspaceOrThrow({ token: platformToken });
      const regions = await getPrismaPostgresRegionsOrThrow({ token: platformToken });
      const ppgRegionSelection = args["--region"] || await select({
        message: "Select your region:",
        default: "us-east-1",
        choices: regions.map((region) => ({
          name: `${region.id} - ${region.displayName}`,
          value: region.id,
          disabled: region.ppgStatus === "unavailable"
        })),
        loop: true
      });
      const projectDisplayNameAnswer = args["--name"] || await input({
        message: "Enter a project name:",
        default: generatedName || "My Prisma Project"
      });
      const spinner = ora(
        `Creating project ${bold2(projectDisplayNameAnswer)} (this may take a few seconds)...`
      ).start();
      try {
        const project = await PlatformCommands.Project.createProjectOrThrow({
          token: platformToken,
          displayName: projectDisplayNameAnswer,
          workspaceId: defaultWorkspace.id,
          allowRemoteDatabases: false,
          ppgRegion: ppgRegionSelection
        });
        spinner.text = `Waiting for your Prisma Postgres database to be ready...`;
        workspaceId = defaultWorkspace.id;
        projectId = project.id;
        environmentId = project.defaultEnvironment.id;
        await poll(
          () => PlatformCommands.Environment.getEnvironmentOrThrow({
            environmentId: project.defaultEnvironment.id,
            token: platformToken
          }),
          (environment) => environment.ppg.status === "healthy" && environment.accelerate.status.enabled,
          5e3,
          // Poll every 5 seconds
          12e4
          // if it takes more than two minutes, bail with an error
        );
        const serviceToken = await PlatformCommands.ServiceToken.createOrThrow({
          token: platformToken,
          environmentId: project.defaultEnvironment.id,
          displayName: `database-setup-prismaPostgres-api-key`
        });
        prismaPostgresDatabaseUrl = `${internals.PRISMA_POSTGRES_PROTOCOL}//accelerate.prisma-data.net/?api_key=${serviceToken.value}`;
        spinner.succeed(successMessage("Your Prisma Postgres database is ready \u2705"));
      } catch (error) {
        spinner.fail(error instanceof Error ? error.message : "Something went wrong");
        throw error;
      }
    }
    if (fs2.existsSync(path2.join(outputDir, "schema.prisma")) || fs2.existsSync(prismaFolder) || fs2.existsSync(path2.join(prismaFolder, "schema.prisma"))) {
      if (isPpgCommand) {
        return printPpgInitOutput({
          databaseUrl: prismaPostgresDatabaseUrl,
          workspaceId,
          projectId,
          environmentId,
          isExistingPrismaProject: true
        });
      }
    }
    if (fs2.existsSync(path2.join(outputDir, "schema.prisma"))) {
      console.log(
        printError(`File ${bold2("schema.prisma")} already exists in your project.
        Please try again in a project that is not yet using Prisma.
      `)
      );
      process.exit(1);
    }
    if (fs2.existsSync(prismaFolder)) {
      console.log(
        printError(`A folder called ${bold2("prisma")} already exists in your project.
        Please try again in a project that is not yet using Prisma.
      `)
      );
      process.exit(1);
    }
    if (fs2.existsSync(path2.join(prismaFolder, "schema.prisma"))) {
      console.log(
        printError(`File ${bold2("prisma/schema.prisma")} already exists in your project.
        Please try again in a project that is not yet using Prisma.
      `)
      );
      process.exit(1);
    }
    if (!fs2.existsSync(outputDir)) {
      fs2.mkdirSync(outputDir);
    }
    if (!fs2.existsSync(prismaFolder)) {
      fs2.mkdirSync(prismaFolder);
    }
    const clientOutput = output ?? determineClientOutputPath(prismaFolder);
    fs2.writeFileSync(
      path2.join(prismaFolder, "schema.prisma"),
      generatedSchema || defaultSchema({
        datasourceProvider,
        generatorProvider,
        previewFeatures,
        output: clientOutput,
        withModel: args["--with-model"]
      })
    );
    const databaseUrl = prismaPostgresDatabaseUrl || url;
    const warnings = [];
    const envPath = path2.join(outputDir, ".env");
    if (!fs2.existsSync(envPath)) {
      fs2.writeFileSync(envPath, defaultEnv(databaseUrl));
    } else {
      const envFile = fs2.readFileSync(envPath, { encoding: "utf8" });
      const config = dotenv.parse(envFile);
      if (Object.keys(config).includes("DATABASE_URL")) {
        warnings.push(
          `${yellow("warn")} Prisma would have added DATABASE_URL but it already exists in ${bold2(
            path2.relative(outputDir, envPath)
          )}`
        );
      } else {
        fs2.appendFileSync(envPath, `

# This was inserted by \`prisma init\`:
` + defaultEnv(databaseUrl));
      }
    }
    const gitignorePath = path2.join(outputDir, ".gitignore");
    try {
      fs2.writeFileSync(gitignorePath, defaultGitIgnore(), { flag: "wx" });
    } catch (e) {
      if (e.code === "EEXIST") {
        warnings.push(
          `${yellow(
            "warn"
          )} You already have a .gitignore file. Don't forget to add \`.env\` in it to not commit any private information.`
        );
      } else {
        console.error("Failed to write .gitignore file, reason: ", e);
      }
    }
    const clientPathRelativeToOutputDir = path2.relative(outputDir, path2.resolve(prismaFolder, clientOutput));
    try {
      fs2.appendFileSync(gitignorePath, `
/${clientPathRelativeToOutputDir.replaceAll(path2.sep, "/")}
`);
    } catch (e) {
      console.error("Failed to append client path to .gitignore file, reason: ", e);
    }
    const steps = [];
    if (datasourceProvider === "mongodb") {
      steps.push(`Define models in the schema.prisma file.`);
    } else {
      steps.push(
        `Run ${green(internals.getCommandWithExecutor("prisma db pull"))} to turn your database schema into a Prisma schema.`
      );
    }
    steps.push(
      `Run ${green(
        internals.getCommandWithExecutor("prisma generate")
      )} to generate the Prisma Client. You can then start querying your database.`
    );
    steps.push(
      `Tip: Explore how you can extend the ${green(
        "ORM"
      )} with scalable connection pooling, global caching, and real-time database events. Read: https://pris.ly/cli/beyond-orm`
    );
    if (!url && !args["--datasource-provider"]) {
      steps.unshift(
        `Set the ${green("provider")} of the ${green("datasource")} block in ${green(
          "schema.prisma"
        )} to match your database: ${green("postgresql")}, ${green("mysql")}, ${green("sqlite")}, ${green(
          "sqlserver"
        )}, ${green("mongodb")} or ${green("cockroachdb")}.`
      );
    }
    if (!args["--url"]) {
      steps.unshift(
        `Set the ${green("DATABASE_URL")} in the ${green(
          ".env"
        )} file to point to your existing database. If your database has no tables yet, read https://pris.ly/d/getting-started`
      );
    }
    const defaultOutput = `
\u2714 Your Prisma schema was created at ${green("prisma/schema.prisma")}
  You can now open it in your favorite editor.
${warnings.length > 0 && internals.logger.should.warn() ? `
${warnings.join("\n")}
` : ""}
Next steps:
${steps.map((s, i) => `${i + 1}. ${s}`).join("\n")}

More information in our documentation:
${internals.link("https://pris.ly/d/getting-started")}
    `;
    return isPpgCommand ? printPpgInitOutput({ databaseUrl: prismaPostgresDatabaseUrl, workspaceId, projectId, environmentId }) : defaultOutput;
  }
  // help message
  help(error) {
    if (error) {
      return new internals.HelpError(`
${bold2(red2(`!`))} ${error}
${_Init.help}`);
    }
    return _Init.help;
  }
};
var DATASOURCE_PROVIDERS = [
  "postgresql",
  "mysql",
  "sqlite",
  "sqlserver",
  "mongodb",
  "cockroachdb",
  "prismapostgres",
  "prisma+postgres"
];
function assertDatasourceProvider(thing) {
  if (typeof thing !== "string" || !DATASOURCE_PROVIDERS.includes(thing)) {
    throw new Error(
      `Provider "${thing}" is invalid or not supported. Try again with ${DATASOURCE_PROVIDERS.slice(0, -1).map((p) => `"${p}"`).join(", ")} or "${DATASOURCE_PROVIDERS.at(-1)}".`
    );
  }
}

// src/index.ts
async function run(args, config) {
  const response = await new Init().parse(args, config);
  if (response instanceof Error) {
    throw response;
  } else {
    console.log(response);
  }
}
export {
  run
};
