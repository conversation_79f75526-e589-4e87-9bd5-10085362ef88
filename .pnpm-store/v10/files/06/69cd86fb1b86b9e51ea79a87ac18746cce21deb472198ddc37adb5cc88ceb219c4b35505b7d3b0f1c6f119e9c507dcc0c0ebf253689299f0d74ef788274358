"use strict";
var import_chunk_HB2Q7X5H = require("../chunk-HB2Q7X5H.js");
var import_chunk_4VNS5WPM = require("../chunk-4VNS5WPM.js");
describe("isPrismaPostgres", () => {
  test("returns false on invalid or non Prisma Postgres protocols", () => {
    expect((0, import_chunk_HB2Q7X5H.isPrismaPostgres)()).toBe(false);
    expect((0, import_chunk_HB2Q7X5H.isPrismaPostgres)("")).toBe(false);
    expect((0, import_chunk_HB2Q7X5H.isPrismaPostgres)("mysql://database.url/test")).toBe(false);
    expect((0, import_chunk_HB2Q7X5H.isPrismaPostgres)("prisma://database.url/test")).toBe(false);
  });
  test("returns true on valid Prisma Postgres protocols", () => {
    expect((0, import_chunk_HB2Q7X5H.isPrismaPostgres)("prisma+postgres://database.url/test")).toBe(true);
    expect((0, import_chunk_HB2Q7X5H.isPrismaPostgres)(`${import_chunk_HB2Q7X5H.PRISMA_POSTGRES_PROTOCOL}//database.url/test`)).toBe(true);
  });
});
