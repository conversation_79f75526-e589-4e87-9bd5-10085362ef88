{"version": 3, "file": "Match.js", "names": ["internal", "Predicate", "MatcherTypeId", "TypeId", "type", "value", "valueTags", "typeTags", "withReturnType", "when", "whenOr", "whenAnd", "discriminator", "discriminatorStartsWith", "discriminators", "discriminatorsExhaustive", "tag", "tagStartsWith", "tags", "tagsExhaustive", "not", "nonEmptyString", "is", "string", "isString", "number", "isNumber", "any", "defined", "boolean", "isBoolean", "_undefined", "isUndefined", "undefined", "_null", "isNull", "null", "bigint", "isBigInt", "symbol", "isSymbol", "date", "isDate", "record", "isRecord", "instanceOf", "instanceOfUnsafe", "orElse", "orElseAbsurd", "either", "option", "exhaustive", "SafeRefinementId", "Symbol", "for", "Fail"], "sources": ["../../src/Match.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAO,KAAKA,QAAQ,MAAM,uBAAuB;AAGjD,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAI3C;;;;AAIA,OAAO,MAAMC,aAAa,GAAkBF,QAAQ,CAACG,MAAM;AA8E3D;;;;AAIA,OAAO,MAAMC,IAAI,GAA+DJ,QAAQ,CAACI,IAAI;AAE7F;;;;AAIA,OAAO,MAAMC,KAAK,GAEmCL,QAAQ,CAACK,KAAK;AAEnE;;;;AAIA,OAAO,MAAMC,SAAS,GASwCN,QAAQ,CAACM,SAAS;AAEhF;;;;AAIA,OAAO,MAAMC,QAAQ,GAQyCP,QAAQ,CAACO,QAAQ;AAE/E;;;;AAIA,OAAO,MAAMC,cAAc,GAGmCR,QAAQ,CAACQ,cAAc;AAErF;;;;AAIA,OAAO,MAAMC,IAAI,GAiBbT,QAAQ,CAACS,IAAI;AAEjB;;;;AAIA,OAAO,MAAMC,MAAM,GAgBfV,QAAQ,CAACU,MAAM;AAEnB;;;;AAIA,OAAO,MAAMC,OAAO,GAehBX,QAAQ,CAACW,OAAO;AAEpB;;;;AAIA,OAAO,MAAMC,aAAa,GAatBZ,QAAQ,CAACY,aAAa;AAE1B;;;;AAIA,OAAO,MAAMC,uBAAuB,GAchCb,QAAQ,CAACa,uBAA8B;AAE3C;;;;AAIA,OAAO,MAAMC,cAAc,GAmBvBd,QAAQ,CAACc,cAAc;AAE3B;;;;AAIA,OAAO,MAAMC,wBAAwB,GAanCf,QAAQ,CAACe,wBAAwB;AAEnC;;;;AAIA,OAAO,MAAMC,GAAG,GAWZhB,QAAQ,CAACgB,GAAG;AAEhB;;;;AAIA,OAAO,MAAMC,aAAa,GAYtBjB,QAAQ,CAACiB,aAAoB;AAEjC;;;;AAIA,OAAO,MAAMC,IAAI,GAiBblB,QAAQ,CAACkB,IAAI;AAEjB;;;;AAIA,OAAO,MAAMC,cAAc,GAWzBnB,QAAQ,CAACmB,cAAc;AAEzB;;;;AAIA,OAAO,MAAMC,GAAG,GAiBZpB,QAAQ,CAACoB,GAAG;AAEhB;;;;AAIA,OAAO,MAAMC,cAAc,GAAkCrB,QAAQ,CAACqB,cAAc;AAEpF;;;;AAIA,OAAO,MAAMC,EAAE,GAE+DtB,QAAQ,CAACsB,EAAE;AAEzF;;;;AAIA,OAAO,MAAMC,MAAM,GAA0CtB,SAAS,CAACuB,QAAQ;AAE/E;;;;AAIA,OAAO,MAAMC,MAAM,GAA0CxB,SAAS,CAACyB,QAAQ;AAE/E;;;;AAIA,OAAO,MAAMC,GAAG,GAAiC3B,QAAQ,CAAC2B,GAAG;AAE7D;;;;AAIA,OAAO,MAAMC,OAAO,GAA6B5B,QAAQ,CAAC4B,OAAO;AAEjE;;;;AAIA,OAAO,MAAMC,OAAO,GAA2C5B,SAAS,CAAC6B,SAAS;AAElF,MAAMC,UAAU,GAA6C9B,SAAS,CAAC+B,WAAW;AAClF;AACE;;;;AAIAD,UAAU,IAAIE,SAAS;AAGzB,MAAMC,KAAK,GAAwCjC,SAAS,CAACkC,MAAM;AACnE;AACE;;;;AAIAD,KAAK,IAAIE,IAAI;AAGf;;;;AAIA,OAAO,MAAMC,MAAM,GAA0CpC,SAAS,CAACqC,QAAQ;AAE/E;;;;AAIA,OAAO,MAAMC,MAAM,GAA0CtC,SAAS,CAACuC,QAAQ;AAE/E;;;;AAIA,OAAO,MAAMC,IAAI,GAAwCxC,SAAS,CAACyC,MAAM;AAEzE;;;;AAIA,OAAO,MAAMC,MAAM,GAAqE1C,SAAS,CAAC2C,QAAQ;AAE1G;;;;AAIA,OAAO,MAAMC,UAAU,GAEuB7C,QAAQ,CAAC6C,UAAU;AAEjE;;;;AAIA,OAAO,MAAMC,gBAAgB,GAE2B9C,QAAQ,CAAC6C,UAAU;AAE3E;;;;AAIA,OAAO,MAAME,MAAM,GAI6E/C,QAAQ,CAAC+C,MAAM;AAE/G;;;;AAIA,OAAO,MAAMC,YAAY,GAEuChD,QAAQ,CAACgD,YAAY;AAErF;;;;AAIA,OAAO,MAAMC,MAAM,GAEiFjD,QAAQ,CAACiD,MAAM;AAEnH;;;;AAIA,OAAO,MAAMC,MAAM,GAE2ElD,QAAQ,CAACkD,MAAM;AAE7G;;;;AAIA,OAAO,MAAMC,UAAU,GAEqCnD,QAAQ,CAACmD,UAAU;AAE/E;;;;AAIA,OAAO,MAAMC,gBAAgB,gBAAGC,MAAM,CAACC,GAAG,CAAC,uBAAuB,CAAC;AAgBnE,MAAMC,IAAI,gBAAGF,MAAM,CAACC,GAAG,CAAC,aAAa,CAAC", "ignoreList": []}