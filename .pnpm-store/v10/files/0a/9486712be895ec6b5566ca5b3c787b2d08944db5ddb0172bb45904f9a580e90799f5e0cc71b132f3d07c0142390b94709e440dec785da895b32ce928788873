!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n||self).tsPattern={})}(this,function(n){function t(n){return t=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},t(n)}function e(n,t){return e=Object.setPrototypeOf||function(n,t){return n.__proto__=t,n},e(n,t)}function r(n,t,u){return r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(n){return!1}}()?Reflect.construct:function(n,t,r){var u=[null];u.push.apply(u,t);var i=new(Function.bind.apply(n,u));return r&&e(i,r.prototype),i},r.apply(null,arguments)}function u(n){var i="function"==typeof Map?new Map:void 0;return u=function(n){if(null===n||-1===Function.toString.call(n).indexOf("[native code]"))return n;if("function"!=typeof n)throw new TypeError("Super expression must either be null or a function");if(void 0!==i){if(i.has(n))return i.get(n);i.set(n,u)}function u(){return r(n,arguments,t(this).constructor)}return u.prototype=Object.create(n.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),e(u,n)},u(n)}function i(n,t){(null==t||t>n.length)&&(t=n.length);for(var e=0,r=new Array(t);e<t;e++)r[e]=n[e];return r}function o(n,t){var e="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(e)return(e=e.call(n)).next.bind(e);if(Array.isArray(n)||(e=function(n,t){if(n){if("string"==typeof n)return i(n,t);var e=Object.prototype.toString.call(n).slice(8,-1);return"Object"===e&&n.constructor&&(e=n.constructor.name),"Map"===e||"Set"===e?Array.from(n):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?i(n,t):void 0}}(n))||t&&n&&"number"==typeof n.length){e&&(n=e);var r=0;return function(){return r>=n.length?{done:!0}:{done:!1,value:n[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var c=Symbol.for("@ts-pattern/matcher"),f=Symbol.for("@ts-pattern/isVariadic"),a="@ts-pattern/anonymous-select-key",l=function(n){return Boolean(n&&"object"==typeof n)},s=function(n){return n&&!!n[c]},h=function n(t,e,r){if(s(t)){var u=t[c]().match(e),i=u.matched,a=u.selections;return i&&a&&Object.keys(a).forEach(function(n){return r(n,a[n])}),i}if(l(t)){if(!l(e))return!1;if(Array.isArray(t)){if(!Array.isArray(e))return!1;for(var h,v=[],p=[],y=[],g=o(t.keys());!(h=g()).done;){var d=t[h.value];s(d)&&d[f]?y.push(d):y.length?p.push(d):v.push(d)}if(y.length){if(y.length>1)throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(e.length<v.length+p.length)return!1;var m=e.slice(0,v.length),b=0===p.length?[]:e.slice(-p.length),w=e.slice(v.length,0===p.length?Infinity:-p.length);return v.every(function(t,e){return n(t,m[e],r)})&&p.every(function(t,e){return n(t,b[e],r)})&&(0===y.length||n(y[0],w,r))}return t.length===e.length&&t.every(function(t,u){return n(t,e[u],r)})}return Reflect.ownKeys(t).every(function(u){var i,o=t[u];return(u in e||s(i=o)&&"optional"===i[c]().matcherType)&&n(o,e[u],r)})}return Object.is(e,t)},v=function n(t){var e,r,u;return l(t)?s(t)?null!=(e=null==(r=(u=t[c]()).getSelectionKeys)?void 0:r.call(u))?e:[]:Array.isArray(t)?p(t,n):p(Object.values(t),n):[]},p=function(n,t){return n.reduce(function(n,e){return n.concat(t(e))},[])};function y(){var n=[].slice.call(arguments);if(1===n.length){var t=n[0];return function(n){return h(t,n,function(){})}}if(2===n.length)return h(n[0],n[1],function(){});throw new Error("isMatching wasn't given the right number of arguments: expected 1 or 2, received "+n.length+".")}function g(n){return Object.assign(n,{optional:function(){return m(n)},and:function(t){return O(n,t)},or:function(t){return S(n,t)},select:function(t){return void 0===t?x(n):x(t,n)}})}function d(n){return Object.assign(function(n){var t;return Object.assign(n,((t={})[Symbol.iterator]=function(){var t,e=0,r=[{value:Object.assign(n,((t={})[f]=!0,t)),done:!1},{done:!0,value:void 0}];return{next:function(){var n;return null!=(n=r[e++])?n:r.at(-1)}}},t))}(n),{optional:function(){return d(m(n))},select:function(t){return d(void 0===t?x(n):x(t,n))}})}function m(n){var t;return g(((t={})[c]=function(){return{match:function(t){var e={},r=function(n,t){e[n]=t};return void 0===t?(v(n).forEach(function(n){return r(n,void 0)}),{matched:!0,selections:e}):{matched:h(n,t,r),selections:e}},getSelectionKeys:function(){return v(n)},matcherType:"optional"}},t))}var b=function(n,t){for(var e,r=o(n);!(e=r()).done;)if(!t(e.value))return!1;return!0},w=function(n,t){for(var e,r=o(n.entries());!(e=r()).done;){var u=e.value;if(!t(u[1],u[0]))return!1}return!0};function O(){var n,t=[].slice.call(arguments);return g(((n={})[c]=function(){return{match:function(n){var e={},r=function(n,t){e[n]=t};return{matched:t.every(function(t){return h(t,n,r)}),selections:e}},getSelectionKeys:function(){return p(t,v)},matcherType:"and"}},n))}function S(){var n,t=[].slice.call(arguments);return g(((n={})[c]=function(){return{match:function(n){var e={},r=function(n,t){e[n]=t};return p(t,v).forEach(function(n){return r(n,void 0)}),{matched:t.some(function(t){return h(t,n,r)}),selections:e}},getSelectionKeys:function(){return p(t,v)},matcherType:"or"}},n))}function j(n){var t;return(t={})[c]=function(){return{match:function(t){return{matched:Boolean(n(t))}}}},t}function x(){var n,t=[].slice.call(arguments),e="string"==typeof t[0]?t[0]:void 0,r=2===t.length?t[1]:"string"==typeof t[0]?void 0:t[0];return g(((n={})[c]=function(){return{match:function(n){var t,u=((t={})[null!=e?e:a]=n,t);return{matched:void 0===r||h(r,n,function(n,t){u[n]=t}),selections:u}},getSelectionKeys:function(){return[null!=e?e:a].concat(void 0===r?[]:v(r))}}},n))}function A(n){return"number"==typeof n}function E(n){return"string"==typeof n}function _(n){return"bigint"==typeof n}var P=g(j(function(n){return!0})),K=P,T=function n(t){return Object.assign(g(t),{startsWith:function(e){return n(O(t,(r=e,j(function(n){return E(n)&&n.startsWith(r)}))));var r},endsWith:function(e){return n(O(t,(r=e,j(function(n){return E(n)&&n.endsWith(r)}))));var r},minLength:function(e){return n(O(t,function(n){return j(function(t){return E(t)&&t.length>=n})}(e)))},length:function(e){return n(O(t,function(n){return j(function(t){return E(t)&&t.length===n})}(e)))},maxLength:function(e){return n(O(t,function(n){return j(function(t){return E(t)&&t.length<=n})}(e)))},includes:function(e){return n(O(t,(r=e,j(function(n){return E(n)&&n.includes(r)}))));var r},regex:function(e){return n(O(t,(r=e,j(function(n){return E(n)&&Boolean(n.match(r))}))));var r}})}(j(E)),B=function n(t){return Object.assign(g(t),{between:function(e,r){return n(O(t,function(n,t){return j(function(e){return A(e)&&n<=e&&t>=e})}(e,r)))},lt:function(e){return n(O(t,function(n){return j(function(t){return A(t)&&t<n})}(e)))},gt:function(e){return n(O(t,function(n){return j(function(t){return A(t)&&t>n})}(e)))},lte:function(e){return n(O(t,function(n){return j(function(t){return A(t)&&t<=n})}(e)))},gte:function(e){return n(O(t,function(n){return j(function(t){return A(t)&&t>=n})}(e)))},int:function(){return n(O(t,j(function(n){return A(n)&&Number.isInteger(n)})))},finite:function(){return n(O(t,j(function(n){return A(n)&&Number.isFinite(n)})))},positive:function(){return n(O(t,j(function(n){return A(n)&&n>0})))},negative:function(){return n(O(t,j(function(n){return A(n)&&n<0})))}})}(j(A)),M=function n(t){return Object.assign(g(t),{between:function(e,r){return n(O(t,function(n,t){return j(function(e){return _(e)&&n<=e&&t>=e})}(e,r)))},lt:function(e){return n(O(t,function(n){return j(function(t){return _(t)&&t<n})}(e)))},gt:function(e){return n(O(t,function(n){return j(function(t){return _(t)&&t>n})}(e)))},lte:function(e){return n(O(t,function(n){return j(function(t){return _(t)&&t<=n})}(e)))},gte:function(e){return n(O(t,function(n){return j(function(t){return _(t)&&t>=n})}(e)))},positive:function(){return n(O(t,j(function(n){return _(n)&&n>0})))},negative:function(){return n(O(t,j(function(n){return _(n)&&n<0})))}})}(j(_)),R=g(j(function(n){return"boolean"==typeof n})),I=g(j(function(n){return"symbol"==typeof n})),N=g(j(function(n){return null==n})),k=g(j(function(n){return null!=n})),W={__proto__:null,matcher:c,optional:m,array:function(){var n,t=[].slice.call(arguments);return d(((n={})[c]=function(){return{match:function(n){if(!Array.isArray(n))return{matched:!1};if(0===t.length)return{matched:!0};var e=t[0],r={};if(0===n.length)return v(e).forEach(function(n){r[n]=[]}),{matched:!0,selections:r};var u=function(n,t){r[n]=(r[n]||[]).concat([t])};return{matched:n.every(function(n){return h(e,n,u)}),selections:r}},getSelectionKeys:function(){return 0===t.length?[]:v(t[0])}}},n))},set:function(){var n,t=[].slice.call(arguments);return g(((n={})[c]=function(){return{match:function(n){if(!(n instanceof Set))return{matched:!1};var e={};if(0===n.size)return{matched:!0,selections:e};if(0===t.length)return{matched:!0};var r=function(n,t){e[n]=(e[n]||[]).concat([t])},u=t[0];return{matched:b(n,function(n){return h(u,n,r)}),selections:e}},getSelectionKeys:function(){return 0===t.length?[]:v(t[0])}}},n))},map:function(){var n,t=[].slice.call(arguments);return g(((n={})[c]=function(){return{match:function(n){if(!(n instanceof Map))return{matched:!1};var e={};if(0===n.size)return{matched:!0,selections:e};var r,u=function(n,t){e[n]=(e[n]||[]).concat([t])};if(0===t.length)return{matched:!0};if(1===t.length)throw new Error("`P.map` wasn't given enough arguments. Expected (key, value), received "+(null==(r=t[0])?void 0:r.toString()));var i=t[0],o=t[1];return{matched:w(n,function(n,t){var e=h(i,t,u),r=h(o,n,u);return e&&r}),selections:e}},getSelectionKeys:function(){return 0===t.length?[]:[].concat(v(t[0]),v(t[1]))}}},n))},intersection:O,union:S,not:function(n){var t;return g(((t={})[c]=function(){return{match:function(t){return{matched:!h(n,t,function(){})}},getSelectionKeys:function(){return[]},matcherType:"not"}},t))},when:j,select:x,any:P,_:K,string:T,number:B,bigint:M,boolean:R,symbol:I,nullish:N,nonNullable:k,instanceOf:function(n){return g(j(function(n){return function(t){return t instanceof n}}(n)))},shape:function(n){return g(j(y(n)))}},F=/*#__PURE__*/function(n){var t,r;function u(t){var e,r;try{r=JSON.stringify(t)}catch(n){r=t}return(e=n.call(this,"Pattern matching error: no pattern matches value "+r)||this).input=void 0,e.input=t,e}return r=n,(t=u).prototype=Object.create(r.prototype),t.prototype.constructor=t,e(t,r),u}(/*#__PURE__*/u(Error)),z={matched:!1,value:void 0},L=/*#__PURE__*/function(){function n(n,t){this.input=void 0,this.state=void 0,this.input=n,this.state=t}var t=n.prototype;return t.with=function(){var t=this,e=[].slice.call(arguments);if(this.state.matched)return this;var r=e[e.length-1],u=[e[0]],i=void 0;3===e.length&&"function"==typeof e[1]?i=e[1]:e.length>2&&u.push.apply(u,e.slice(1,e.length-1));var o=!1,c={},f=function(n,t){o=!0,c[n]=t},l=!u.some(function(n){return h(n,t.input,f)})||i&&!Boolean(i(this.input))?z:{matched:!0,value:r(o?a in c?c[a]:c:this.input,this.input)};return new n(this.input,l)},t.when=function(t,e){if(this.state.matched)return this;var r=Boolean(t(this.input));return new n(this.input,r?{matched:!0,value:e(this.input,this.input)}:z)},t.otherwise=function(n){return this.state.matched?this.state.value:n(this.input)},t.exhaustive=function(){if(this.state.matched)return this.state.value;throw new F(this.input)},t.run=function(){return this.exhaustive()},t.returnType=function(){return this},n}();n.NonExhaustiveError=F,n.P=W,n.Pattern=W,n.isMatching=y,n.match=function(n){return new L(n,z)}});
//# sourceMappingURL=index.umd.js.map
