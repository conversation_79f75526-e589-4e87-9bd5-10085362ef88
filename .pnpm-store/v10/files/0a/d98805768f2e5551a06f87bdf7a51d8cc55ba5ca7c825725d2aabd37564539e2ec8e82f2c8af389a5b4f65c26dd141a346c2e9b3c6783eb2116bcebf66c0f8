{"version": 3, "file": "circular.js", "names": ["Context", "_interopRequireWildcard", "require", "_Function", "HashSet", "core", "fiberRuntime", "layer", "runtimeFlags", "runtimeFlagsPatch", "supervisor_", "tracer", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "minimumLogLevel", "level", "scopedDiscard", "fiberRefLocallyScoped", "currentMinimumLogLevel", "exports", "withMinimumLogLevel", "dual", "self", "fiberRefLocally", "<PERSON><PERSON><PERSON>ger", "logger", "fiberRefLocallyScopedWith", "currentLoggers", "add", "addLoggerEffect", "effect", "unwrapEffect", "map", "addLoggerScoped", "unwrapScoped", "<PERSON><PERSON><PERSON><PERSON>", "remove", "<PERSON><PERSON><PERSON><PERSON>", "that", "flatMap", "replaceLoggerEffect", "replaceLoggerScoped", "addSupervisor", "supervisor", "currentSupervisor", "current", "Zip", "enableCooperativeYielding", "withRuntimeFlagsScoped", "enable", "CooperativeYielding", "enableInterruption", "Interruption", "enableOpSupervision", "OpSupervision", "enableRuntimeMetrics", "RuntimeMetrics", "enableWindDown", "WindDown", "disableCooperativeYielding", "disable", "disableInterruption", "disableOpSupervision", "disableRuntimeMetrics", "disableWindDown", "setConfigProvider", "config<PERSON><PERSON><PERSON>", "withConfigProviderScoped", "parentSpan", "span", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "make", "spanTag", "name", "options", "addSpanStackTrace", "scoped", "onEnd", "tap", "makeSpanScoped", "addFinalizer", "exit", "setTracer", "withTracerScoped"], "sources": ["../../../../src/internal/layer/circular.ts"], "sourcesContent": [null], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAH,uBAAA,CAAAC,OAAA;AAOA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,YAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AACA,IAAAM,YAAA,GAAAP,uBAAA,CAAAC,OAAA;AACA,IAAAO,iBAAA,GAAAR,uBAAA,CAAAC,OAAA;AACA,IAAAQ,WAAA,GAAAT,uBAAA,CAAAC,OAAA;AACA,IAAAS,MAAA,GAAAV,uBAAA,CAAAC,OAAA;AAAsC,SAAAU,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAZ,wBAAAY,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEtC;AAEA;AACO,MAAMW,eAAe,GAAIC,KAAwB,IACtD1B,KAAK,CAAC2B,aAAa,CACjB5B,YAAY,CAAC6B,qBAAqB,CAChC7B,YAAY,CAAC8B,sBAAsB,EACnCH,KAAK,CACN,CACF;AAEH;AAAAI,OAAA,CAAAL,eAAA,GAAAA,eAAA;AACO,MAAMM,mBAAmB,GAAAD,OAAA,CAAAC,mBAAA,gBAAG,IAAAC,cAAI,EAGrC,CAAC,EAAE,CAACC,IAAI,EAAEP,KAAK,KACf5B,IAAI,CAACoC,eAAe,CAClBnC,YAAY,CAAC8B,sBAAsB,EACnCH,KAAK,CACN,CAACO,IAAI,CAAC,CAAC;AAEV;AACO,MAAME,SAAS,GAAOC,MAAiC,IAC5DpC,KAAK,CAAC2B,aAAa,CACjB5B,YAAY,CAACsC,yBAAyB,CACpCtC,YAAY,CAACuC,cAAc,EAC3BzC,OAAO,CAAC0C,GAAG,CAACH,MAAM,CAAC,CACpB,CACF;AAEH;AAAAN,OAAA,CAAAK,SAAA,GAAAA,SAAA;AACO,MAAMK,eAAe,GAC1BC,MAAsD,IAEtDzC,KAAK,CAAC0C,YAAY,CAChB5C,IAAI,CAAC6C,GAAG,CAACF,MAAM,EAAEN,SAAS,CAAC,CAC5B;AAEH;AAAAL,OAAA,CAAAU,eAAA,GAAAA,eAAA;AACO,MAAMI,eAAe,GAC1BH,MAAsD,IAEtDzC,KAAK,CAAC6C,YAAY,CAChB/C,IAAI,CAAC6C,GAAG,CAACF,MAAM,EAAEN,SAAS,CAAC,CAC5B;AAEH;AAAAL,OAAA,CAAAc,eAAA,GAAAA,eAAA;AACO,MAAME,YAAY,GAAOV,MAAiC,IAC/DpC,KAAK,CAAC2B,aAAa,CACjB5B,YAAY,CAACsC,yBAAyB,CACpCtC,YAAY,CAACuC,cAAc,EAC3BzC,OAAO,CAACkD,MAAM,CAACX,MAAM,CAAC,CACvB,CACF;AAEH;AAAAN,OAAA,CAAAgB,YAAA,GAAAA,YAAA;AACO,MAAME,aAAa,GAAAlB,OAAA,CAAAkB,aAAA,gBAAG,IAAAhB,cAAI,EAG/B,CAAC,EAAE,CAACC,IAAI,EAAEgB,IAAI,KAAKjD,KAAK,CAACkD,OAAO,CAACJ,YAAY,CAACb,IAAI,CAAC,EAAE,MAAME,SAAS,CAACc,IAAI,CAAC,CAAC,CAAC;AAE9E;AACO,MAAME,mBAAmB,GAAArB,OAAA,CAAAqB,mBAAA,gBAAG,IAAAnB,cAAI,EAQrC,CAAC,EAAE,CAACC,IAAI,EAAEgB,IAAI,KAAKjD,KAAK,CAACkD,OAAO,CAACJ,YAAY,CAACb,IAAI,CAAC,EAAE,MAAMO,eAAe,CAACS,IAAI,CAAC,CAAC,CAAC;AAEpF;AACO,MAAMG,mBAAmB,GAAAtB,OAAA,CAAAsB,mBAAA,gBAAG,IAAApB,cAAI,EAQrC,CAAC,EAAE,CAACC,IAAI,EAAEgB,IAAI,KAAKjD,KAAK,CAACkD,OAAO,CAACJ,YAAY,CAACb,IAAI,CAAC,EAAE,MAAMW,eAAe,CAACK,IAAI,CAAC,CAAC,CAAC;AAEpF;AACO,MAAMI,aAAa,GAAOC,UAAoC,IACnEtD,KAAK,CAAC2B,aAAa,CACjB5B,YAAY,CAACsC,yBAAyB,CACpCtC,YAAY,CAACwD,iBAAiB,EAC7BC,OAAO,IAAK,IAAIrD,WAAW,CAACsD,GAAG,CAACD,OAAO,EAAEF,UAAU,CAAC,CACtD,CACF;AAEH;AAAAxB,OAAA,CAAAuB,aAAA,GAAAA,aAAA;AACO,MAAMK,yBAAyB,GAAA5B,OAAA,CAAA4B,yBAAA,gBAAuB1D,KAAK,CAAC2B,aAAa,eAC9E5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAAC0D,MAAM,CAAC3D,YAAY,CAAC4D,mBAAmB,CAAC,CAC3D,CACF;AAED;AACO,MAAMC,kBAAkB,GAAAhC,OAAA,CAAAgC,kBAAA,gBAAuB9D,KAAK,CAAC2B,aAAa,eACvE5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAAC0D,MAAM,CAAC3D,YAAY,CAAC8D,YAAY,CAAC,CACpD,CACF;AAED;AACO,MAAMC,mBAAmB,GAAAlC,OAAA,CAAAkC,mBAAA,gBAAuBhE,KAAK,CAAC2B,aAAa,eACxE5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAAC0D,MAAM,CAAC3D,YAAY,CAACgE,aAAa,CAAC,CACrD,CACF;AAED;AACO,MAAMC,oBAAoB,GAAApC,OAAA,CAAAoC,oBAAA,gBAAuBlE,KAAK,CAAC2B,aAAa,eACzE5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAAC0D,MAAM,CAAC3D,YAAY,CAACkE,cAAc,CAAC,CACtD,CACF;AAED;AACO,MAAMC,cAAc,GAAAtC,OAAA,CAAAsC,cAAA,gBAAuBpE,KAAK,CAAC2B,aAAa,eACnE5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAAC0D,MAAM,CAAC3D,YAAY,CAACoE,QAAQ,CAAC,CAChD,CACF;AAED;AACO,MAAMC,0BAA0B,GAAAxC,OAAA,CAAAwC,0BAAA,gBAAuBtE,KAAK,CAAC2B,aAAa,eAC/E5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAACqE,OAAO,CAACtE,YAAY,CAAC4D,mBAAmB,CAAC,CAC5D,CACF;AAED;AACO,MAAMW,mBAAmB,GAAA1C,OAAA,CAAA0C,mBAAA,gBAAuBxE,KAAK,CAAC2B,aAAa,eACxE5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAACqE,OAAO,CAACtE,YAAY,CAAC8D,YAAY,CAAC,CACrD,CACF;AAED;AACO,MAAMU,oBAAoB,GAAA3C,OAAA,CAAA2C,oBAAA,gBAAuBzE,KAAK,CAAC2B,aAAa,eACzE5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAACqE,OAAO,CAACtE,YAAY,CAACgE,aAAa,CAAC,CACtD,CACF;AAED;AACO,MAAMS,qBAAqB,GAAA5C,OAAA,CAAA4C,qBAAA,gBAAuB1E,KAAK,CAAC2B,aAAa,eAC1E5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAACqE,OAAO,CAACtE,YAAY,CAACkE,cAAc,CAAC,CACvD,CACF;AAED;AACO,MAAMQ,eAAe,GAAA7C,OAAA,CAAA6C,eAAA,gBAAuB3E,KAAK,CAAC2B,aAAa,eACpE5B,YAAY,CAAC4D,sBAAsB,eACjCzD,iBAAiB,CAACqE,OAAO,CAACtE,YAAY,CAACoE,QAAQ,CAAC,CACjD,CACF;AAED;AACO,MAAMO,iBAAiB,GAAIC,cAA6C,IAC7E7E,KAAK,CAAC2B,aAAa,CAAC5B,YAAY,CAAC+E,wBAAwB,CAACD,cAAc,CAAC,CAAC;AAE5E;AAAA/C,OAAA,CAAA8C,iBAAA,GAAAA,iBAAA;AACO,MAAMG,UAAU,GAAIC,IAAoB,IAC7ChF,KAAK,CAACiF,cAAc,CAACxF,OAAO,CAACyF,IAAI,CAAC9E,MAAM,CAAC+E,OAAO,EAAEH,IAAI,CAAC,CAAC;AAE1D;AAAAlD,OAAA,CAAAiD,UAAA,GAAAA,UAAA;AACO,MAAMC,IAAI,GAAGA,CAClBI,IAAY,EACZC,OAIC,KACiC;EAClCA,OAAO,GAAGjF,MAAM,CAACkF,iBAAiB,CAACD,OAAO,CAAQ;EAClD,OAAOrF,KAAK,CAACuF,MAAM,CACjBnF,MAAM,CAAC+E,OAAO,EACdE,OAAO,EAAEG,KAAK,GACV1F,IAAI,CAAC2F,GAAG,CACR1F,YAAY,CAAC2F,cAAc,CAACN,IAAI,EAAEC,OAAO,CAAC,EACzCL,IAAI,IAAKjF,YAAY,CAAC4F,YAAY,CAAEC,IAAI,IAAKP,OAAO,CAACG,KAAM,CAACR,IAAI,EAAEY,IAAI,CAAC,CAAC,CAC1E,GACC7F,YAAY,CAAC2F,cAAc,CAACN,IAAI,EAAEC,OAAO,CAAC,CAC/C;AACH,CAAC;AAED;AAAAvD,OAAA,CAAAkD,IAAA,GAAAA,IAAA;AACO,MAAMa,SAAS,GAAIzF,MAAqB,IAC7CJ,KAAK,CAAC2B,aAAa,CAAC5B,YAAY,CAAC+F,gBAAgB,CAAC1F,MAAM,CAAC,CAAC;AAAA0B,OAAA,CAAA+D,SAAA,GAAAA,SAAA", "ignoreList": []}