{"version": 3, "file": "UpstreamPullStrategy.js", "names": ["internal", "UpstreamPullStrategyTypeId", "PullAfterNext", "PullAfterAllEnqueued", "isUpstreamPullStrategy", "isPullAfterNext", "isPullAfterAllEnqueued", "match"], "sources": ["../../src/UpstreamPullStrategy.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,QAAQ,MAAM,4CAA4C;AAItE;;;;AAIA,OAAO,MAAMC,0BAA0B,GAAkBD,QAAQ,CAACC,0BAA0B;AA+C5F;;;;AAIA,OAAO,MAAMC,aAAa,GAAoEF,QAAQ,CAACE,aAAa;AAEpH;;;;AAIA,OAAO,MAAMC,oBAAoB,GAC/BH,QAAQ,CAACG,oBAAoB;AAE/B;;;;;;;AAOA,OAAO,MAAMC,sBAAsB,GACjCJ,QAAQ,CAACI,sBAAsB;AAEjC;;;;;;;AAOA,OAAO,MAAMC,eAAe,GAAmEL,QAAQ,CAACK,eAAe;AAEvH;;;;;;;AAOA,OAAO,MAAMC,sBAAsB,GACjCN,QAAQ,CAACM,sBAAsB;AAEjC;;;;;;AAMA,OAAO,MAAMC,KAAK,GA0BdP,QAAQ,CAACO,KAAK", "ignoreList": []}