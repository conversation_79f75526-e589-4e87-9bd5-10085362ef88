{"version": 3, "file": "base64Url.js", "names": ["Either", "Base64", "DecodeException", "encode", "data", "replace", "decode", "str", "stripped", "stripCrlf", "length", "left", "test", "sanitized"], "sources": ["../../../../src/internal/encoding/base64Url.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,MAAM,MAAM,iBAAiB;AAEzC,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,SAASC,eAAe,QAAQ,aAAa;AAE7C;AACA,OAAO,MAAMC,MAAM,GAAIC,IAAgB,IACrCH,MAAM,CAACE,MAAM,CAACC,IAAI,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;AAE/E;AACA,OAAO,MAAMC,MAAM,GAAIC,GAAW,IAAyD;EACzF,MAAMC,QAAQ,GAAGP,MAAM,CAACQ,SAAS,CAACF,GAAG,CAAC;EACtC,MAAMG,MAAM,GAAGF,QAAQ,CAACE,MAAM;EAC9B,IAAIA,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACpB,OAAOV,MAAM,CAACW,IAAI,CAChBT,eAAe,CAACM,QAAQ,EAAE,4CAA4CE,MAAM,EAAE,CAAC,CAChF;EACH;EAEA,IAAI,CAAC,uBAAuB,CAACE,IAAI,CAACJ,QAAQ,CAAC,EAAE;IAC3C,OAAOR,MAAM,CAACW,IAAI,CAACT,eAAe,CAACM,QAAQ,EAAE,eAAe,CAAC,CAAC;EAChE;EAEA;EACA,IAAIK,SAAS,GAAGH,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAGF,QAAQ,IAAI,GAAGE,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,GAAGF,QAAQ,GAAG,GAAGA,QAAQ;EACjGK,SAAS,GAAGA,SAAS,CAACR,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EAE3D,OAAOJ,MAAM,CAACK,MAAM,CAACO,SAAS,CAAC;AACjC,CAAC", "ignoreList": []}