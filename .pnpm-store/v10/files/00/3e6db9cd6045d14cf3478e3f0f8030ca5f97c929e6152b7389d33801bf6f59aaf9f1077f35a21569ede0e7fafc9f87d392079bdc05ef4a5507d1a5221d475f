import type { Arbitrary } from '../check/arbitrary/definition/Arbitrary.js';
/**
 * For single unicode characters - any of the code points defined in the unicode standard
 *
 * WARNING: Generated values can have a length greater than 1.
 *
 * {@link https://tc39.github.io/ecma262/#sec-utf16encoding}
 *
 * @deprecated Please use ${@link string} with `fc.string({ unit: 'grapheme', minLength: 1, maxLength: 1 })` or `fc.string({ unit: 'binary', minLength: 1, maxLength: 1 })` instead
 * @remarks Since 0.0.11
 * @public
 */
export declare function fullUnicode(): Arbitrary<string>;
