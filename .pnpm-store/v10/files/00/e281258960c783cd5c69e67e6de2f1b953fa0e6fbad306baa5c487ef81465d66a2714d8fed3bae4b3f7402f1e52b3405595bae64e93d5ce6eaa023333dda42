{"version": 3, "file": "contextPatch.js", "names": ["Chunk", "Equal", "Dual", "makeContext", "Structural", "ContextPatchTypeId", "Symbol", "for", "variance", "a", "PatchProto", "prototype", "_Value", "_Patch", "EmptyProto", "Object", "assign", "create", "_tag", "_empty", "empty", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "makeAndThen", "first", "second", "o", "AddServiceProto", "makeAddService", "key", "service", "RemoveServiceProto", "makeRemoveService", "UpdateServiceProto", "makeUpdateService", "update", "diff", "oldValue", "newValue", "missingServices", "Map", "unsafeMap", "patch", "tag", "newService", "entries", "has", "old", "get", "delete", "equals", "combine", "dual", "self", "that", "context", "wasServiceUpdated", "patches", "of", "updatedContext", "isNonEmpty", "head", "headNonEmpty", "tail", "tailNonEmpty", "set", "prepend", "map", "s"], "sources": ["../../../../src/internal/differ/contextPatch.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,gBAAgB;AAGvC,OAAO,KAAKC,KAAK,MAAM,gBAAgB;AACvC,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,YAAY;AAEvC;AACA,OAAO,MAAMC,kBAAkB,gBAA0BC,MAAM,CAACC,GAAG,CACjE,2BAA2B,CACH;AAE1B,SAASC,QAAQA,CAAOC,CAAI;EAC1B,OAAOA,CAAiB;AAC1B;AAEA;AACA,MAAMC,UAAU,GAAG;EACjB,GAAGN,UAAU,CAACO,SAAS;EACvB,CAACN,kBAAkB,GAAG;IACpBO,MAAM,EAAEJ,QAAQ;IAChBK,MAAM,EAAEL;;CAEX;AAMD,MAAMM,UAAU,gBAAGC,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACP,UAAU,CAAC,EAAE;EAC1DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMC,MAAM,gBAAGJ,MAAM,CAACE,MAAM,CAACH,UAAU,CAAC;AAExC;;;AAGA,OAAO,MAAMM,KAAK,GAAGA,CAAA,KAA0DD,MAAM;AASrF,MAAME,YAAY,gBAAGN,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACP,UAAU,CAAC,EAAE;EAC5DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMI,WAAW,GAAGA,CAClBC,KAA0C,EAC1CC,MAA6C,KACL;EACxC,MAAMC,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACI,YAAY,CAAC;EACrCI,CAAC,CAACF,KAAK,GAAGA,KAAK;EACfE,CAAC,CAACD,MAAM,GAAGA,MAAM;EACjB,OAAOC,CAAC;AACV,CAAC;AASD,MAAMC,eAAe,gBAAGX,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACP,UAAU,CAAC,EAAE;EAC/DQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMS,cAAc,GAAGA,CACrBC,GAAW,EACXC,OAAU,KAC4B;EACtC,MAAMJ,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACS,eAAe,CAAC;EACxCD,CAAC,CAACG,GAAG,GAAGA,GAAG;EACXH,CAAC,CAACI,OAAO,GAAGA,OAAO;EACnB,OAAOJ,CAAC;AACV,CAAC;AAQD,MAAMK,kBAAkB,gBAAGf,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACP,UAAU,CAAC,EAAE;EAClEQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMa,iBAAiB,GACrBH,GAAW,IACmC;EAC9C,MAAMH,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACa,kBAAkB,CAAC;EAC3CL,CAAC,CAACG,GAAG,GAAGA,GAAG;EACX,OAAOH,CAAC;AACV,CAAC;AASD,MAAMO,kBAAkB,gBAAGjB,MAAM,CAACC,MAAM,eAACD,MAAM,CAACE,MAAM,CAACP,UAAU,CAAC,EAAE;EAClEQ,IAAI,EAAE;CACP,CAAC;AAEF,MAAMe,iBAAiB,GAAGA,CACxBL,GAAW,EACXM,MAAyB,KACiB;EAC1C,MAAMT,CAAC,GAAGV,MAAM,CAACE,MAAM,CAACe,kBAAkB,CAAC;EAC3CP,CAAC,CAACG,GAAG,GAAGA,GAAG;EACXH,CAAC,CAACS,MAAM,GAAGA,MAAM;EACjB,OAAOT,CAAC;AACV,CAAC;AASD;AACA,OAAO,MAAMU,IAAI,GAAGA,CAClBC,QAAwB,EACxBC,QAAyB,KACc;EACvC,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAACH,QAAQ,CAACI,SAAS,CAAC;EACnD,IAAIC,KAAK,GAAGrB,KAAK,EAAY;EAC7B,KAAK,MAAM,CAACsB,GAAG,EAAEC,UAAU,CAAC,IAAIN,QAAQ,CAACG,SAAS,CAACI,OAAO,EAAE,EAAE;IAC5D,IAAIN,eAAe,CAACO,GAAG,CAACH,GAAG,CAAC,EAAE;MAC5B,MAAMI,GAAG,GAAGR,eAAe,CAACS,GAAG,CAACL,GAAG,CAAE;MACrCJ,eAAe,CAACU,MAAM,CAACN,GAAG,CAAC;MAC3B,IAAI,CAACzC,KAAK,CAACgD,MAAM,CAACH,GAAG,EAAEH,UAAU,CAAC,EAAE;QAClCF,KAAK,GAAGS,OAAO,CAACjB,iBAAiB,CAACS,GAAG,EAAE,MAAMC,UAAU,CAAC,CAAC,CAACF,KAAK,CAAC;MAClE;IACF,CAAC,MAAM;MACLH,eAAe,CAACU,MAAM,CAACN,GAAG,CAAC;MAC3BD,KAAK,GAAGS,OAAO,CAACvB,cAAc,CAACe,GAAG,EAAEC,UAAU,CAAC,CAAC,CAACF,KAAK,CAAC;IACzD;EACF;EACA,KAAK,MAAM,CAACC,GAAG,CAAC,IAAIJ,eAAe,CAACM,OAAO,EAAE,EAAE;IAC7CH,KAAK,GAAGS,OAAO,CAACnB,iBAAiB,CAACW,GAAG,CAAC,CAAC,CAACD,KAAK,CAAC;EAChD;EACA,OAAOA,KAAK;AACd,CAAC;AAED;AACA,OAAO,MAAMS,OAAO,gBAAGhD,IAAI,CAACiD,IAAI,CAU9B,CAAC,EAAE,CAACC,IAAI,EAAEC,IAAI,KAAK/B,WAAW,CAAC8B,IAAI,EAAEC,IAAI,CAAC,CAAC;AAE7C;AACA,OAAO,MAAMZ,KAAK,gBAAGvC,IAAI,CAACiD,IAAI,CAU5B,CAAC,EAAE,CAAgBC,IAAyC,EAAEE,OAAuB,KAAI;EACzF,IAAKF,IAAoB,CAAClC,IAAI,KAAK,OAAO,EAAE;IAC1C,OAAOoC,OAAc;EACvB;EACA,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAIC,OAAO,GAAwDxD,KAAK,CAACyD,EAAE,CACzEL,IAA8C,CAC/C;EACD,MAAMM,cAAc,GAAyB,IAAInB,GAAG,CAACe,OAAO,CAACd,SAAS,CAAC;EACvE,OAAOxC,KAAK,CAAC2D,UAAU,CAACH,OAAO,CAAC,EAAE;IAChC,MAAMI,IAAI,GAAgB5D,KAAK,CAAC6D,YAAY,CAACL,OAAO,CAAgB;IACpE,MAAMM,IAAI,GAAG9D,KAAK,CAAC+D,YAAY,CAACP,OAAO,CAAC;IACxC,QAAQI,IAAI,CAAC1C,IAAI;MACf,KAAK,OAAO;QAAE;UACZsC,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,YAAY;QAAE;UACjBJ,cAAc,CAACM,GAAG,CAACJ,IAAI,CAAChC,GAAG,EAAEgC,IAAI,CAAC/B,OAAO,CAAC;UAC1C2B,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,SAAS;QAAE;UACdN,OAAO,GAAGxD,KAAK,CAACiE,OAAO,CAACjE,KAAK,CAACiE,OAAO,CAACH,IAAI,EAAEF,IAAI,CAACpC,MAAM,CAAC,EAAEoC,IAAI,CAACrC,KAAK,CAAC;UACrE;QACF;MACA,KAAK,eAAe;QAAE;UACpBmC,cAAc,CAACV,MAAM,CAACY,IAAI,CAAChC,GAAG,CAAC;UAC/B4B,OAAO,GAAGM,IAAI;UACd;QACF;MACA,KAAK,eAAe;QAAE;UACpBJ,cAAc,CAACM,GAAG,CAACJ,IAAI,CAAChC,GAAG,EAAEgC,IAAI,CAAC1B,MAAM,CAACwB,cAAc,CAACX,GAAG,CAACa,IAAI,CAAChC,GAAG,CAAC,CAAC,CAAC;UACvE2B,iBAAiB,GAAG,IAAI;UACxBC,OAAO,GAAGM,IAAI;UACd;QACF;IACF;EACF;EACA,IAAI,CAACP,iBAAiB,EAAE;IACtB,OAAOpD,WAAW,CAACuD,cAAc,CAAoB;EACvD;EACA,MAAMQ,GAAG,GAAG,IAAI3B,GAAG,EAAE;EACrB,KAAK,MAAM,CAACG,GAAG,CAAC,IAAIY,OAAO,CAACd,SAAS,EAAE;IACrC,IAAIkB,cAAc,CAACb,GAAG,CAACH,GAAG,CAAC,EAAE;MAC3BwB,GAAG,CAACF,GAAG,CAACtB,GAAG,EAAEgB,cAAc,CAACX,GAAG,CAACL,GAAG,CAAC,CAAC;MACrCgB,cAAc,CAACV,MAAM,CAACN,GAAG,CAAC;IAC5B;EACF;EACA,KAAK,MAAM,CAACA,GAAG,EAAEyB,CAAC,CAAC,IAAIT,cAAc,EAAE;IACrCQ,GAAG,CAACF,GAAG,CAACtB,GAAG,EAAEyB,CAAC,CAAC;EACjB;EACA,OAAOhE,WAAW,CAAC+D,GAAG,CAAoB;AAC5C,CAAC,CAAC", "ignoreList": []}