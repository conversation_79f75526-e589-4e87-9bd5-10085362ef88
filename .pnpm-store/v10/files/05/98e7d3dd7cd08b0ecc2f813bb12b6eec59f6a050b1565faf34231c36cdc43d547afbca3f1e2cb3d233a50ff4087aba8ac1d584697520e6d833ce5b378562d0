{"version": 3, "file": "string-utils.js", "names": ["lowerCase", "str", "toLowerCase", "upperCase", "toUpperCase", "replace", "input", "re", "value", "RegExp", "reduce", "DEFAULT_SPLIT_REGEXP", "DEFAULT_STRIP_REGEXP", "noCase", "options", "delimiter", "splitRegexp", "stripRegexp", "transform", "result", "start", "end", "length", "char<PERSON>t", "slice", "split", "map", "join", "pascalCaseTransform", "index", "firstChar", "lowerChars", "substring", "pascalCase", "camelCaseTransform", "camelCase", "constantCase", "kebabCase", "snakeCase"], "sources": ["../../../src/internal/string-utils.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;;AAMA;AACA,OAAO,MAAMA,SAAS,GAAIC,GAAW,IAAKA,GAAG,CAACC,WAAW,EAAE;AAE3D;AACA,OAAO,MAAMC,SAAS,GAAIF,GAAW,IAAKA,GAAG,CAACG,WAAW,EAAE;AAS3D;;;AAGA,MAAMC,OAAO,GAAGA,CAACC,KAAa,EAAEC,EAAkC,EAAEC,KAAa,KAC/ED,EAAE,YAAYE,MAAM,GAChBH,KAAK,CAACD,OAAO,CAACE,EAAE,EAAEC,KAAK,CAAC,GACxBD,EAAE,CAACG,MAAM,CAAC,CAACJ,KAAK,EAAEC,EAAE,KAAKD,KAAK,CAACD,OAAO,CAACE,EAAE,EAAEC,KAAK,CAAC,EAAEF,KAAK,CAAC;AAE/D;AACA,MAAMK,oBAAoB,GAAG,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;AAE3E;AACA,MAAMC,oBAAoB,GAAG,cAAc;AAE3C;;;AAGA,MAAMC,MAAM,GAAGA,CAACP,KAAa,EAAEQ,OAAA,GAAmB,EAAE,KAAY;EAC9D,MAAM;IACJC,SAAS,GAAG,GAAG;IACfC,WAAW,GAAGL,oBAAoB;IAClCM,WAAW,GAAGL,oBAAoB;IAClCM,SAAS,GAAGlB;EAAS,CACtB,GAAGc,OAAO;EACX,MAAMK,MAAM,GAAGd,OAAO,CAACA,OAAO,CAACC,KAAK,EAAEU,WAAW,EAAE,QAAQ,CAAC,EAAEC,WAAW,EAAE,IAAI,CAAC;EAChF,IAAIG,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGF,MAAM,CAACG,MAAM;EACvB;EACA,OAAOH,MAAM,CAACI,MAAM,CAACH,KAAK,CAAC,KAAK,IAAI,EAAE;IACpCA,KAAK,EAAE;EACT;EACA,OAAOD,MAAM,CAACI,MAAM,CAACF,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE;IACtCA,GAAG,EAAE;EACP;EACA;EACA,OAAOF,MAAM,CAACK,KAAK,CAACJ,KAAK,EAAEC,GAAG,CAAC,CAACI,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAACR,SAAS,CAAC,CAACS,IAAI,CAACZ,SAAS,CAAC;AAC5E,CAAC;AAED,MAAMa,mBAAmB,GAAGA,CAACtB,KAAa,EAAEuB,KAAa,KAAY;EACnE,MAAMC,SAAS,GAAGxB,KAAK,CAACiB,MAAM,CAAC,CAAC,CAAC;EACjC,MAAMQ,UAAU,GAAGzB,KAAK,CAAC0B,SAAS,CAAC,CAAC,CAAC,CAAC9B,WAAW,EAAE;EACnD,IAAI2B,KAAK,GAAG,CAAC,IAAIC,SAAS,IAAI,GAAG,IAAIA,SAAS,IAAI,GAAG,EAAE;IACrD,OAAO,IAAIA,SAAS,GAAGC,UAAU,EAAE;EACrC;EACA,OAAO,GAAGD,SAAS,CAAC1B,WAAW,EAAE,GAAG2B,UAAU,EAAE;AAClD,CAAC;AAED;AACA,OAAO,MAAME,UAAU,GAAGA,CAAC3B,KAAa,EAAEQ,OAAiB,KACzDD,MAAM,CAACP,KAAK,EAAE;EACZS,SAAS,EAAE,EAAE;EACbG,SAAS,EAAEU,mBAAmB;EAC9B,GAAGd;CACJ,CAAC;AAEJ,MAAMoB,kBAAkB,GAAGA,CAAC5B,KAAa,EAAEuB,KAAa,KACtDA,KAAK,KAAK,CAAC,GACPvB,KAAK,CAACJ,WAAW,EAAE,GACnB0B,mBAAmB,CAACtB,KAAK,EAAEuB,KAAK,CAAC;AAEvC;AACA,OAAO,MAAMM,SAAS,GAAGA,CAAC7B,KAAa,EAAEQ,OAAiB,KACxDmB,UAAU,CAAC3B,KAAK,EAAE;EAChBY,SAAS,EAAEgB,kBAAkB;EAC7B,GAAGpB;CACJ,CAAC;AAEJ;AACA,OAAO,MAAMsB,YAAY,GAAGA,CAAC9B,KAAa,EAAEQ,OAAiB,KAC3DD,MAAM,CAACP,KAAK,EAAE;EACZS,SAAS,EAAE,GAAG;EACdG,SAAS,EAAEf,SAAS;EACpB,GAAGW;CACJ,CAAC;AAEJ;AACA,OAAO,MAAMuB,SAAS,GAAGA,CAAC/B,KAAa,EAAEQ,OAAiB,KACxDD,MAAM,CAACP,KAAK,EAAE;EACZS,SAAS,EAAE,GAAG;EACd,GAAGD;CACJ,CAAC;AAEJ;AACA,OAAO,MAAMwB,SAAS,GAAGA,CAAChC,KAAa,EAAEQ,OAAiB,KACxDD,MAAM,CAACP,KAAK,EAAE;EACZS,SAAS,EAAE,GAAG;EACd,GAAGD;CACJ,CAAC", "ignoreList": []}