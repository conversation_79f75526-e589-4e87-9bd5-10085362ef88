{"version": 3, "file": "tDeferred.js", "names": ["Either", "_interopRequireWildcard", "require", "_Function", "Option", "core", "stm", "tRef", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "TDeferredSymbolKey", "TDeferredTypeId", "exports", "Symbol", "for", "tD<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_A", "_", "_E", "TDeferredImpl", "ref", "constructor", "_await", "self", "flatten", "collect", "option", "isSome", "some", "fromEither", "value", "none", "done", "dual", "either", "flatMap", "match", "onNone", "zipRight", "succeed", "onSome", "fail", "error", "left", "make", "map", "poll", "right"], "sources": ["../../../../src/internal/stm/tDeferred.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,uBAAA,CAAAC,OAAA;AAIA,IAAAG,IAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,GAAA,GAAAL,uBAAA,CAAAC,OAAA;AACA,IAAAK,IAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAiC,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAEjC;AACA,MAAMW,kBAAkB,GAAG,kBAAkB;AAE7C;AACO,MAAMC,eAAe,GAAAC,OAAA,CAAAD,eAAA,gBAA8BE,MAAM,CAACC,GAAG,CAClEJ,kBAAkB,CACU;AAE9B;AACA,MAAMK,iBAAiB,GAAG;EACxB;EACAC,EAAE,EAAGC,CAAM,IAAKA,CAAC;EACjB;EACAC,EAAE,EAAGD,CAAM,IAAKA;CACjB;AAED;AACA,MAAME,aAAa;EAEIC,GAAA;EADZ,CAACT,eAAe,IAAII,iBAAiB;EAC9CM,YAAqBD,GAAkD;IAAlD,KAAAA,GAAG,GAAHA,GAAG;EAAkD;;AAG5E;AACO,MAAME,MAAM,GAAUC,IAA+B,IAC1DnC,GAAG,CAACoC,OAAO,CACTpC,GAAG,CAACqC,OAAO,CAACpC,IAAI,CAACS,GAAG,CAACyB,IAAI,CAACH,GAAG,CAAC,EAAGM,MAAM,IACrCxC,MAAM,CAACyC,MAAM,CAACD,MAAM,CAAC,GACnBxC,MAAM,CAAC0C,IAAI,CAACxC,GAAG,CAACyC,UAAU,CAACH,MAAM,CAACI,KAAK,CAAC,CAAC,GACzC5C,MAAM,CAAC6C,IAAI,EAAE,CAAC,CACnB;AAEH;AAAAnB,OAAA,CAAAU,MAAA,GAAAA,MAAA;AACO,MAAMU,IAAI,GAAApB,OAAA,CAAAoB,IAAA,gBAAG,IAAAC,cAAI,EAGtB,CAAC,EAAE,CAACV,IAAI,EAAEW,MAAM,KAChB/C,IAAI,CAACgD,OAAO,CACV9C,IAAI,CAACS,GAAG,CAACyB,IAAI,CAACH,GAAG,CAAC,EAClBlC,MAAM,CAACkD,KAAK,CAAC;EACXC,MAAM,EAAEA,CAAA,KACNlD,IAAI,CAACmD,QAAQ,CACXjD,IAAI,CAACoB,GAAG,CAACc,IAAI,CAACH,GAAG,EAAElC,MAAM,CAAC0C,IAAI,CAACM,MAAM,CAAC,CAAC,EACvC/C,IAAI,CAACoD,OAAO,CAAC,IAAI,CAAC,CACnB;EACHC,MAAM,EAAEA,CAAA,KAAMrD,IAAI,CAACoD,OAAO,CAAC,KAAK;CACjC,CAAC,CACH,CAAC;AAEJ;AACO,MAAME,IAAI,GAAA7B,OAAA,CAAA6B,IAAA,gBAAG,IAAAR,cAAI,EAGtB,CAAC,EAAE,CAACV,IAAI,EAAEmB,KAAK,KAAKV,IAAI,CAACT,IAAI,EAAEzC,MAAM,CAAC6D,IAAI,CAACD,KAAK,CAAC,CAAC,CAAC;AAErD;AACO,MAAME,IAAI,GAAGA,CAAA,KAClBzD,IAAI,CAAC0D,GAAG,CACNxD,IAAI,CAACuD,IAAI,CAAqC1D,MAAM,CAAC6C,IAAI,EAAE,CAAC,EAC3DX,GAAG,IAAK,IAAID,aAAa,CAACC,GAAG,CAAC,CAChC;AAEH;AAAAR,OAAA,CAAAgC,IAAA,GAAAA,IAAA;AACO,MAAME,IAAI,GACfvB,IAA+B,IACiBlC,IAAI,CAACS,GAAG,CAACyB,IAAI,CAACH,GAAG,CAAC;AAEpE;AAAAR,OAAA,CAAAkC,IAAA,GAAAA,IAAA;AACO,MAAMP,OAAO,GAAA3B,OAAA,CAAA2B,OAAA,gBAAG,IAAAN,cAAI,EAGzB,CAAC,EAAE,CAACV,IAAI,EAAEO,KAAK,KAAKE,IAAI,CAACT,IAAI,EAAEzC,MAAM,CAACiE,KAAK,CAACjB,KAAK,CAAC,CAAC,CAAC", "ignoreList": []}