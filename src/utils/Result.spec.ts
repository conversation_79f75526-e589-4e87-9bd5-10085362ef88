import { describe, it, expect } from 'vitest'
import { ok, err, isOk, isErr, type Result } from './Result'

describe('Result', () => {
  describe('ok', () => {
    it('should create a success result', () => {
      const result = ok(42)
      expect(result.ok).toBe(true)
      expect(result.value).toBe(42)
    })

    it('should be identified as success by isOk', () => {
      const result = ok('test')
      expect(isOk(result)).toBe(true)
      expect(isErr(result)).toBe(false)
    })
  })

  describe('err', () => {
    it('should create a failure result', () => {
      const result = err('error message')
      expect(result.ok).toBe(false)
      expect(result.error).toBe('error message')
    })

    it('should be identified as error by isErr', () => {
      const result = err('error')
      expect(isErr(result)).toBe(true)
      expect(isOk(result)).toBe(false)
    })
  })

  describe('map method chaining', () => {
    it('should chain successful transformations', () => {
      const result = ok(5)
        .map(x => x * 2)
        .map(x => x.toString())
        .map(x => x + '!')

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.value).toBe('10!')
      }
    })

    it('should stop at first error in chain', () => {
      const result = ok(5)
        .map(x => x * 2)
        .map(x => {
          if (x > 5) throw new Error('Too large')
          return x
        })
        .map(x => x.toString())

      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBeInstanceOf(Error)
        expect((result.error as Error).message).toBe('Too large')
      }
    })

    it('should handle Result-returning functions in map', () => {
      const result = ok(5)
        .map(x => x > 3 ? ok(x * 2) : err('Too small'))
        .map(x => x.toString())

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.value).toBe('10')
      }
    })

    it('should propagate errors from Result-returning functions', () => {
      const result = ok(2)
        .map(x => x > 3 ? ok(x * 2) : err('Too small'))
        .map(x => x.toString())

      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBe('Too small')
      }
    })

    it('should not execute map on error results', () => {
      let executed = false
      const result = err('initial error')
        .map(x => {
          executed = true
          return x
        })

      expect(executed).toBe(false)
      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBe('initial error')
      }
    })
  })

  describe('Railway Oriented Programming patterns', () => {
    it('should validate and transform data in a pipeline', () => {
      const validatePositive = (n: number): Result<number, string> =>
        n > 0 ? ok(n) : err('Number must be positive')

      const validateLessThan100 = (n: number): Result<number, string> =>
        n < 100 ? ok(n) : err('Number must be less than 100')

      const result = ok(50)
        .map(validatePositive)
        .map(validateLessThan100)
        .map(n => n * 2)

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.value).toBe(100)
      }
    })

    it('should fail early in validation pipeline', () => {
      const validatePositive = (n: number): Result<number, string> =>
        n > 0 ? ok(n) : err('Number must be positive')

      const validateLessThan100 = (n: number): Result<number, string> =>
        n < 100 ? ok(n) : err('Number must be less than 100')

      const result = ok(-5)
        .map(validatePositive)
        .map(validateLessThan100)
        .map(n => n * 2)

      expect(isErr(result)).toBe(true)
      if (isErr(result)) {
        expect(result.error).toBe('Number must be positive')
      }
    })

    it('should handle complex data processing', () => {
      interface User {
        name: string
        age: number
      }

      const validateName = (user: User): Result<User, string> =>
        user.name.trim().length > 0 ? ok(user) : err('Name cannot be empty')

      const validateAge = (user: User): Result<User, string> =>
        user.age >= 18 ? ok(user) : err('User must be 18 or older')

      const normalizeUser = (user: User): User => ({
        ...user,
        name: user.name.trim().toUpperCase()
      })

      const result = ok({ name: '  john  ', age: 25 })
        .map(normalizeUser)
        .map(validateName)
        .map(validateAge)

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.value.name).toBe('JOHN')
        expect(result.value.age).toBe(25)
      }
    })

    it('should handle JSON parsing with error handling', () => {
      const parseJson = (jsonStr: string): any => {
        return JSON.parse(jsonStr)
      }

      const validResult = ok('{"name": "test"}')
        .map(parseJson)
        .map(obj => obj.name)

      expect(isOk(validResult)).toBe(true)
      if (isOk(validResult)) {
        expect(validResult.value).toBe('test')
      }

      const invalidResult = ok('invalid json')
        .map(parseJson)
        .map(obj => obj.name)

      expect(isErr(invalidResult)).toBe(true)
    })
  })

  describe('type safety', () => {
    it('should maintain type safety through transformations', () => {
      const result: Result<string, Error> = ok(42)
        .map(x => x.toString())
        .map(s => s.length > 0 ? ok(s) : err(new Error('Empty string')))

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(typeof result.value).toBe('string')
        expect(result.value).toBe('42')
      }
    })
  })

  describe('real-world usage examples', () => {
    it('should handle user input validation pipeline', () => {
      const processUserInput = (input: string) =>
        ok(input)
          .map(s => s.trim())
          .map(s => s.length > 0 ? ok(s) : err('Input cannot be empty'))
          .map(s => s.length <= 50 ? ok(s) : err('Input too long'))
          .map(s => parseInt(s))
          .map(n => isNaN(n) ? err('Must be a number') : ok(n))
          .map(n => n > 0 ? ok(n) : err('Must be positive'))

      // Valid input
      const validResult = processUserInput('  42  ')
      expect(isOk(validResult)).toBe(true)
      if (isOk(validResult)) {
        expect(validResult.value).toBe(42)
      }

      // Invalid input - empty
      const emptyResult = processUserInput('   ')
      expect(isErr(emptyResult)).toBe(true)
      if (isErr(emptyResult)) {
        expect(emptyResult.error).toBe('Input cannot be empty')
      }

      // Invalid input - not a number
      const nanResult = processUserInput('abc')
      expect(isErr(nanResult)).toBe(true)
      if (isErr(nanResult)) {
        expect(nanResult.error).toBe('Must be a number')
      }
    })

    it('should demonstrate isOk pattern for final result checking', () => {
      const processData = (data: unknown) =>
        ok(data)
          .map(d => d===null ? err('Must not be null') : ok(d))
          .map(d => typeof d === 'object' ? ok(d) : err('Must be object'))
          .map(d => d.name ? ok(d) : err('Missing name'))
          .map(d => ({ ...d, processed: true }))

      const result = processData({ name: 'test' })

      // This is the recommended pattern - use isOk to check the final result
      if (isOk(result)) {
        // Type-safe access to result.value
        expect(result.value.name).toBe('test')
        expect(result.value.processed).toBe(true)
      } else {
        // Type-safe access to result.error
        throw new Error(`Unexpected error: ${result.error}`)
      }
    })

    it('should handle async-like operations with map chaining', () => {
      // Simulating async operations that return Results
      const fetchUser = (id: number): Result<{ id: number, name: string }, string> =>
        id > 0 ? ok({ id, name: `User${id}` }) : err('Invalid user ID')

      const validateUser = (user: { id: number, name: string }): Result<{ id: number, name: string }, string> =>
        user.name.length > 0 ? ok(user) : err('Invalid user name')

      const enrichUser = (user: { id: number, name: string }) => ({
        ...user,
        displayName: `${user.name} (#${user.id})`
      })

      const result = ok(123)
        .map(fetchUser)
        .map(validateUser)
        .map(enrichUser)

      expect(isOk(result)).toBe(true)
      if (isOk(result)) {
        expect(result.value.displayName).toBe('User123 (#123)')
      }
    })
  })
})
