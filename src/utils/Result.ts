/**
 * A Result type representing either a success value or an error.
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

/**
 * Success case of Result with chainable methods
 */
export interface Success<T> {
  readonly ok: true;
  readonly value: T;

  // Method chaining for Railway Oriented Programming
  map<U>(fn: (value: T) => U): Result<U, never>;
  flatMap<U, E>(fn: (value: T) => Result<U, E>): Result<U, E>;
  mapError<F>(fn: (error: never) => F): Result<T, F>;
  tap(fn: (value: T) => void): Result<T, never>;
  tapError(fn: (error: never) => void): Result<T, never>;
  validate<E>(predicate: (value: T) => boolean, errorFn: (value: T) => E): Result<T, E>;
  recover<E>(fn: (error: never) => T): Result<T, E>;
  recoverWith<E>(fn: (error: never) => Result<T, E>): Result<T, E>;
  unwrap(): T;
  unwrapOr(defaultValue: T): T;
  isOk(): this is Success<T>;
  isErr(): this is Failure<never>;
}

/**
 * Failure case of Result with chainable methods
 */
export interface Failure<E> {
  readonly ok: false;
  readonly error: E;

  // Method chaining for Railway Oriented Programming
  map<U>(fn: (value: never) => U): Result<U, E>;
  flatMap<U, F>(fn: (value: never) => Result<U, F>): Result<U, E>;
  mapError<F>(fn: (error: E) => F): Result<never, F>;
  tap(fn: (value: never) => void): Result<never, E>;
  tapError(fn: (error: E) => void): Result<never, E>;
  validate<F>(predicate: (value: never) => boolean, errorFn: (value: never) => F): Result<never, E>;
  recover<T>(fn: (error: E) => T): Result<T, E>;
  recoverWith<T>(fn: (error: E) => Result<T, E>): Result<T, E>;
  unwrap(): never;
  unwrapOr<T>(defaultValue: T): T;
  isOk(): this is Success<never>;
  isErr(): this is Failure<E>;
}

/**
 * Creates a success result with chainable methods
 */
export function ok<T>(value: T): Success<T> {
  return new SuccessImpl(value);
}

/**
 * Creates a failure result with chainable methods
 */
export function err<E>(error: E): Failure<E> {
  return new FailureImpl(error);
}

/**
 * Implementation of Success with chainable methods
 */
class SuccessImpl<T> implements Success<T> {
  readonly ok = true as const;

  constructor(readonly value: T) {}

  map<U>(fn: (value: T) => U): Result<U, never> {
    return ok(fn(this.value));
  }

  flatMap<U, E>(fn: (value: T) => Result<U, E>): Result<U, E> {
    return fn(this.value);
  }

  mapError<F>(_fn: (error: never) => F): Result<T, F> {
    return this as any;
  }

  tap(fn: (value: T) => void): Result<T, never> {
    fn(this.value);
    return this as any;
  }

  tapError(_fn: (error: never) => void): Result<T, never> {
    return this as any;
  }

  validate<E>(predicate: (value: T) => boolean, errorFn: (value: T) => E): Result<T, E> {
    if (predicate(this.value)) {
      return this as any;
    }
    return err(errorFn(this.value));
  }

  recover<E>(_fn: (error: never) => T): Result<T, E> {
    return this as any;
  }

  recoverWith<E>(_fn: (error: never) => Result<T, E>): Result<T, E> {
    return this as any;
  }

  unwrap(): T {
    return this.value;
  }

  unwrapOr(_defaultValue: T): T {
    return this.value;
  }

  isOk(): this is Success<T> {
    return true;
  }

  isErr(): this is Failure<never> {
    return false;
  }
}

/**
 * Implementation of Failure with chainable methods
 */
class FailureImpl<E> implements Failure<E> {
  readonly ok = false as const;

  constructor(readonly error: E) {}

  map<U>(_fn: (value: never) => U): Result<U, E> {
    return this as any;
  }

  flatMap<U, F>(_fn: (value: never) => Result<U, F>): Result<U, E> {
    return this as any;
  }

  mapError<F>(fn: (error: E) => F): Result<never, F> {
    return err(fn(this.error));
  }

  tap(_fn: (value: never) => void): Result<never, E> {
    return this as any;
  }

  tapError(fn: (error: E) => void): Result<never, E> {
    fn(this.error);
    return this as any;
  }

  validate<F>(_predicate: (value: never) => boolean, _errorFn: (value: never) => F): Result<never, E> {
    return this as any;
  }

  recover<T>(fn: (error: E) => T): Result<T, E> {
    return ok(fn(this.error));
  }

  recoverWith<T>(fn: (error: E) => Result<T, E>): Result<T, E> {
    return fn(this.error);
  }

  unwrap(): never {
    throw this.error;
  }

  unwrapOr<T>(defaultValue: T): T {
    return defaultValue;
  }

  isOk(): this is Success<never> {
    return false;
  }

  isErr(): this is Failure<E> {
    return true;
  }
}

export function isOk<T, E>(result: Result<T, E>): result is Success<T> {
  return result.ok;
}

export function isErr<T, E>(result: Result<T, E>): result is Failure<E> {
  return !result.ok;
}

// ============================================================================
// Method Chain Utilities for Railway Oriented Programming
// ============================================================================

/**
 * Converts a value to the success track
 */
export function succeed<T>(value: T): Result<T, never> {
  return ok(value);
}

/**
 * Converts a value to the failure track
 */
export function fail<E>(error: E): Result<never, E> {
  return err(error);
}

/**
 * Creates a railway function from a predicate
 */
export function guard<T, E>(
  predicate: (value: T) => boolean,
  errorFn: (value: T) => E
): (value: T) => Result<T, E> {
  return (value) => {
    if (predicate(value)) {
      return ok(value);
    }
    return err(errorFn(value));
  };
}

// ============================================================================
// Utility Functions for Method Chaining
// ============================================================================

/**
 * Wraps a function that might throw into a Result-returning function
 */
export function tryCatch<T, E = Error>(
  fn: () => T,
  errorHandler?: (error: unknown) => E
): Result<T, E> {
  try {
    return ok(fn());
  } catch (error) {
    const handledError = errorHandler ? errorHandler(error) : (error as E);
    return err(handledError);
  }
}

/**
 * Wraps an async function that might throw into a Result-returning async function
 */
export async function tryCatchAsync<T, E = Error>(
  fn: () => Promise<T>,
  errorHandler?: (error: unknown) => E
): Promise<Result<T, E>> {
  try {
    const value = await fn();
    return ok(value);
  } catch (error) {
    const handledError = errorHandler ? errorHandler(error) : (error as E);
    return err(handledError);
  }
}

// ============================================================================
// Additional ROP Utilities
// ============================================================================

/**
 * Combines multiple Results into a single Result containing an array
 * Returns the first error encountered, or success with all values
 */
export function combine<T, E>(results: Array<Result<T, E>>): Result<T[], E> {
  const values: T[] = [];

  for (const result of results) {
    if (isErr(result)) {
      return result;
    }
    values.push(result.value);
  }

  return ok(values);
}

/**
 * Gets the value from a Result or throws the error
 */
export function unwrap<T, E>(result: Result<T, E>): T {
  if (isOk(result)) {
    return result.value;
  }
  throw result.error;
}

/**
 * Gets the value from a Result or returns a default value
 */
export function unwrapOr<T, E>(defaultValue: T): (result: Result<T, E>) => T {
  return (result) => {
    if (isOk(result)) {
      return result.value;
    }
    return defaultValue;
  };
}



/**
 * Parallel execution of multiple railway functions
 * All functions receive the same input, results are combined
 */
export function parallel<T, U, E>(
  ...fns: Array<(value: T) => Result<U, E>>
): (value: T) => Result<U[], E> {
  return (value) => {
    const results = fns.map(fn => fn(value));
    return combine(results);
  };
}

/**
 * Creates a railway function that applies the first successful function
 * Tries functions in order until one succeeds
 */
export function firstSuccess<T, U, E>(
  ...fns: Array<(value: T) => Result<U, E>>
): (value: T) => Result<U, E[]> {
  return (value) => {
    const errors: E[] = [];

    for (const fn of fns) {
      const result = fn(value);
      if (isOk(result)) {
        return result;
      }
      errors.push(result.error);
    }

    return err(errors);
  };
}

// ============================================================================
// Additional Method Chain Helpers
// ============================================================================

/**
 * Creates a Result from a value that might be null or undefined
 */
export function fromNullable<T, E>(
  value: T | null | undefined,
  errorFn: () => E
): Result<T, E> {
  if (value != null) {
    return ok(value);
  }
  return err(errorFn());
}

/**
 * Creates a Result from a boolean condition
 */
export function fromCondition<T, E>(
  condition: boolean,
  successValue: T,
  errorValue: E
): Result<T, E> {
  if (condition) {
    return ok(successValue);
  }
  return err(errorValue);
}

/**
 * Converts a Promise to a Result
 */
export async function fromPromise<T, E = Error>(
  promise: Promise<T>,
  errorHandler?: (error: unknown) => E
): Promise<Result<T, E>> {
  try {
    const value = await promise;
    return ok(value);
  } catch (error) {
    const handledError = errorHandler ? errorHandler(error) : (error as E);
    return err(handledError);
  }
}

