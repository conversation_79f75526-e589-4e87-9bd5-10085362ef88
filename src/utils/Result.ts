/**
 * A Result type representing either a success value or an error.
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

/**
 * Success case of Result
 */
export interface Success<T> {
  readonly ok: true;
  readonly value: T;
}

/**
 * Failure case of Result
 */
export interface Failure<E> {
  readonly ok: false;
  readonly error: E;
}

/**
 * Creates a success result
 */
export function ok<T>(value: T): Success<T> {
  return {
    ok: true,
    value,
  };
}

/**
 * Creates a failure result
 */
export function err<E>(error: E): Failure<E> {
  return {
    ok: false,
    error,
  };
}

export function isOk<T, E>(result: Result<T, E>): result is Success<T> {
  return result.ok;
}

export function isErr<T, E>(result: Result<T, E>): result is Failure<E> {
  return !result.ok;
}

// ============================================================================
// Railway Oriented Programming (ROP) Core Functions
// ============================================================================

/**
 * Maps the success value of a Result (lift function in ROP)
 */
export function map<T, U, E>(
  fn: (value: T) => U
): (result: Result<T, E>) => Result<U, E> {
  return (result) => {
    if (isOk(result)) {
      return ok(fn(result.value));
    }
    return result;
  };
}

/**
 * FlatMaps/chains a Result-returning function (bind function in ROP)
 */
export function flatMap<T, U, E>(
  fn: (value: T) => Result<U, E>
): (result: Result<T, E>) => Result<U, E> {
  return (result) => {
    if (isOk(result)) {
      return fn(result.value);
    }
    return result;
  };
}

/**
 * Maps the error value of a Result
 */
export function mapError<T, E, F>(
  fn: (error: E) => F
): (result: Result<T, E>) => Result<T, F> {
  return (result) => {
    if (isErr(result)) {
      return err(fn(result.error));
    }
    return result;
  };
}

/**
 * Executes a side effect without changing the Result (tee function in ROP)
 */
export function tap<T, E>(
  fn: (value: T) => void
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isOk(result)) {
      fn(result.value);
    }
    return result;
  };
}

/**
 * Executes a side effect on error without changing the Result
 */
export function tapError<T, E>(
  fn: (error: E) => void
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isErr(result)) {
      fn(result.error);
    }
    return result;
  };
}

/**
 * Recovers from an error by providing a default value
 */
export function recover<T, E>(
  fn: (error: E) => T
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isErr(result)) {
      return ok(fn(result.error));
    }
    return result;
  };
}

/**
 * Recovers from an error by providing a Result
 */
export function recoverWith<T, E>(
  fn: (error: E) => Result<T, E>
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isErr(result)) {
      return fn(result.error);
    }
    return result;
  };
}

// ============================================================================
// Railway Oriented Programming (ROP) Specific Utilities
// ============================================================================

/**
 * Converts a regular function to work on the success track (ROP lift)
 */
export function lift<T, U, E>(fn: (value: T) => U): (result: Result<T, E>) => Result<U, E> {
  return map(fn);
}

/**
 * Converts a function that might fail to work on the success track (ROP bind)
 */
export function bind<T, U, E>(fn: (value: T) => Result<U, E>): (result: Result<T, E>) => Result<U, E> {
  return flatMap(fn);
}

/**
 * Creates a switch function that routes to success or failure tracks
 */
export function switch_<T, U, V, E>(
  successFn: (value: T) => Result<U, E>,
  failureFn: (error: E) => Result<V, E>
): (result: Result<T, E>) => Result<U | V, E> {
  return (result) => {
    if (isOk(result)) {
      return successFn(result.value);
    }
    return failureFn(result.error);
  };
}

/**
 * Creates a tee function for side effects on both tracks
 */
export function tee<T, E>(
  successFn?: (value: T) => void,
  failureFn?: (error: E) => void
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isOk(result) && successFn) {
      successFn(result.value);
    } else if (isErr(result) && failureFn) {
      failureFn(result.error);
    }
    return result;
  };
}

/**
 * Creates a dead-end function that always returns success
 */
export function deadEnd<T, E>(fn: (value: T) => void): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isOk(result)) {
      try {
        fn(result.value);
      } catch {
        // Ignore errors in dead-end functions
      }
    }
    return result;
  };
}

/**
 * Validates a value and puts it on the success or failure track
 */
export function validate<T, E>(
  predicate: (value: T) => boolean,
  errorFn: (value: T) => E
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isOk(result)) {
      if (predicate(result.value)) {
        return result;
      }
      return err(errorFn(result.value));
    }
    return result;
  };
}

/**
 * Converts a value to the success track
 */
export function succeed<T>(value: T): Result<T, never> {
  return ok(value);
}

/**
 * Converts a value to the failure track
 */
export function fail<E>(error: E): Result<never, E> {
  return err(error);
}

// ============================================================================
// Pipeline and Composition Functions for ROP
// ============================================================================

/**
 * Type for a function that transforms a Result
 */
export type ResultTransformer<T, U, E, F = E> = (result: Result<T, E>) => Result<U, F>;

/**
 * Pipes a Result through a series of transformations (Railway composition)
 * Stops at the first error encountered
 */
export function pipe<T, E>(
  result: Result<T, E>
): Result<T, E>;
export function pipe<T, U, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>
): Result<U, E>;
export function pipe<T, U, V, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>
): Result<V, E>;
export function pipe<T, U, V, W, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>
): Result<W, E>;
export function pipe<T, U, V, W, X, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>
): Result<X, E>;
export function pipe<T, U, V, W, X, Y, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>,
  fn5: ResultTransformer<X, Y, E>
): Result<Y, E>;
export function pipe(result: Result<any, any>, ...fns: Array<ResultTransformer<any, any, any>>): Result<any, any> {
  return fns.reduce((acc, fn) => {
    if (isErr(acc)) {
      return acc;
    }
    return fn(acc);
  }, result);
}

/**
 * Creates a reusable railway pipeline
 */
export function createPipeline<T, U, E>(
  fn1: ResultTransformer<T, U, E>
): (result: Result<T, E>) => Result<U, E>;
export function createPipeline<T, U, V, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>
): (result: Result<T, E>) => Result<V, E>;
export function createPipeline<T, U, V, W, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>
): (result: Result<T, E>) => Result<W, E>;
export function createPipeline<T, U, V, W, X, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>
): (result: Result<T, E>) => Result<X, E>;
export function createPipeline<T, U, V, W, X, Y, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>,
  fn5: ResultTransformer<X, Y, E>
): (result: Result<T, E>) => Result<Y, E>;
export function createPipeline(...fns: Array<ResultTransformer<any, any, any>>): (result: Result<any, any>) => Result<any, any> {
  return (result) => pipe(result, ...(fns as [ResultTransformer<any, any, any>, ...ResultTransformer<any, any, any>[]]));
}

/**
 * Composes two railway functions (right to left composition)
 */
export function compose<T, U, V, E>(
  f: ResultTransformer<U, V, E>,
  g: ResultTransformer<T, U, E>
): ResultTransformer<T, V, E> {
  return (result) => pipe(result, g, f);
}

/**
 * Wraps a function that might throw into a Result-returning function
 */
export function tryCatch<T, E = Error>(
  fn: () => T,
  errorHandler?: (error: unknown) => E
): Result<T, E> {
  try {
    return ok(fn());
  } catch (error) {
    const handledError = errorHandler ? errorHandler(error) : (error as E);
    return err(handledError);
  }
}

/**
 * Wraps an async function that might throw into a Result-returning async function
 */
export async function tryCatchAsync<T, E = Error>(
  fn: () => Promise<T>,
  errorHandler?: (error: unknown) => E
): Promise<Result<T, E>> {
  try {
    const value = await fn();
    return ok(value);
  } catch (error) {
    const handledError = errorHandler ? errorHandler(error) : (error as E);
    return err(handledError);
  }
}

// ============================================================================
// Additional ROP Utilities
// ============================================================================

/**
 * Combines multiple Results into a single Result containing an array
 * Returns the first error encountered, or success with all values
 */
export function combine<T, E>(results: Array<Result<T, E>>): Result<T[], E> {
  const values: T[] = [];

  for (const result of results) {
    if (isErr(result)) {
      return result;
    }
    values.push(result.value);
  }

  return ok(values);
}

/**
 * Gets the value from a Result or throws the error
 */
export function unwrap<T, E>(result: Result<T, E>): T {
  if (isOk(result)) {
    return result.value;
  }
  throw result.error;
}

/**
 * Gets the value from a Result or returns a default value
 */
export function unwrapOr<T, E>(defaultValue: T): (result: Result<T, E>) => T {
  return (result) => {
    if (isOk(result)) {
      return result.value;
    }
    return defaultValue;
  };
}

/**
 * Creates a railway function from a predicate
 */
export function guard<T, E>(
  predicate: (value: T) => boolean,
  errorFn: (value: T) => E
): (value: T) => Result<T, E> {
  return (value) => {
    if (predicate(value)) {
      return ok(value);
    }
    return err(errorFn(value));
  };
}

/**
 * Parallel execution of multiple railway functions
 * All functions receive the same input, results are combined
 */
export function parallel<T, U, E>(
  ...fns: Array<(value: T) => Result<U, E>>
): (value: T) => Result<U[], E> {
  return (value) => {
    const results = fns.map(fn => fn(value));
    return combine(results);
  };
}

/**
 * Creates a railway function that applies the first successful function
 * Tries functions in order until one succeeds
 */
export function firstSuccess<T, U, E>(
  ...fns: Array<(value: T) => Result<U, E>>
): (value: T) => Result<U, E[]> {
  return (value) => {
    const errors: E[] = [];

    for (const fn of fns) {
      const result = fn(value);
      if (isOk(result)) {
        return result;
      }
      errors.push(result.error);
    }

    return err(errors);
  };
}

