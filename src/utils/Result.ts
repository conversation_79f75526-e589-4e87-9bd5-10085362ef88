/**
 * A Result type representing either a success value or an error.
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

/**
 * Success case of Result
 */
export interface Success<T> {
  readonly ok: true;
  readonly value: T;
}

/**
 * Failure case of Result
 */
export interface Failure<E> {
  readonly ok: false;
  readonly error: E;
}

/**
 * Creates a success result
 */
export function ok<T>(value: T): Success<T> {
  return {
    ok: true,
    value,
  };
}

/**
 * Creates a failure result
 */
export function err<E>(error: E): Failure<E> {
  return {
    ok: false,
    error,
  };
}

/**
 * Utility functions for Result
 */
export const ResultUtils = {
  /**
   * Maps a Result<T, E> to Result<U, E> by applying a function to the success value
   */
  map<T, E, U>(result: Result<T, E>, fn: (value: T) => U): Result<U, E> {
    return result.ok ? ok(fn(result.value)) : result;
  },

  /**
   * Maps a Result<T, E> to Result<T, F> by applying a function to the error value
   */
  mapError<T, E, F>(result: Result<T, E>, fn: (error: E) => F): Result<T, F> {
    return result.ok ? result : err(fn(result.error));
  },

  /**
   * Applies a function to the success value and returns the result
   */
  flatMap<T, E, U>(result: Result<T, E>, fn: (value: T) => Result<U, E>): Result<U, E> {
    return result.ok ? fn(result.value) : result;
  },

  /**
   * Returns the success value or throws the error
   */
  unwrap<T, E>(result: Result<T, E>): T {
    if (result.ok) {
      return result.value;
    }
    throw result.error;
  },

  /**
   * Returns the success value or a default value
   */
  unwrapOr<T, E>(result: Result<T, E>, defaultValue: T): T {
    return result.ok ? result.value : defaultValue;
  }
};