/**
 * A Result type representing either a success value or an error.
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

/**
 * Success case of Result with chainable methods
 */
export interface Success<T> {
  readonly ok: true;
  readonly value: T;

  // Core Railway Oriented Programming method
  map<U>(fn: (value: T) => U | Result<U>): Result<U>;
}

/**
 * Failure case of Result with chainable methods
 */
export interface Failure<E> {
  readonly ok: false;
  readonly error: E;

  // Core Railway Oriented Programming method
  map<U>(fn: (value: never) => U | Result<U>): Result<U>;
}

/**
 * Creates a success result with chainable methods
 */
export function ok<T>(value: T): Success<T> {
  return new SuccessImpl(value);
}

/**
 * Creates a failure result with chainable methods
 */
export function err<E>(error: E): Failure<E> {
  return new FailureImpl(error);
}

/**
 * Implementation of Success with chainable methods
 */
class SuccessImpl<T> implements Success<T> {
  readonly ok = true as const;

  constructor(readonly value: T) {}

  map<U>(fn: (value: T) => U | Result<U>): Result<U> {
    const result = fn(this.value);
    // If the function returns a Result, return it directly
    if (result && typeof result === 'object' && 'ok' in result) {
    return result as Result<U>;
    }
    // Otherwise, wrap the value in a success Result
    return ok(result as U);
  }

}

/**
 * Implementation of Failure with chainable methods
 */
class FailureImpl<E> implements Failure<E> {
  readonly ok = false as const;

  constructor(readonly error: E) {}

  map<U>(_fn: (value: never) => U | Result<U>): Result<U> { // eslint-disable-line @typescript-eslint/no-unused-vars
    return this as Failure<E> as Result<U>;
  }
}

export function isOk<T, E>(result: Result<T, E>): result is Success<T> {
  return result.ok;
}

export function isErr<T, E>(result: Result<T, E>): result is Failure<E> {
  return !result.ok;
}
