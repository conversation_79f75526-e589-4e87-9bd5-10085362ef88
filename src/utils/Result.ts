/**
 * A Result type representing either a success value or an error.
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

/**
 * Success case of Result
 */
export interface Success<T> {
  readonly ok: true;
  readonly value: T;
}

/**
 * Failure case of Result
 */
export interface Failure<E> {
  readonly ok: false;
  readonly error: E;
}

/**
 * Creates a success result
 */
export function ok<T>(value: T): Success<T> {
  return {
    ok: true,
    value,
  };
}

/**
 * Creates a failure result
 */
export function err<E>(error: E): Failure<E> {
  return {
    ok: false,
    error,
  };
}

export function isOk<T, E>(result: Result<T, E>): result is Success<T> {
  return result.ok;
}

export function isErr<T, E>(result: Result<T, E>): result is Failure<E> {
  return !result.ok;
}

/**
 * Maps the success value of a Result
 */
export function map<T, U, E>(
  fn: (value: T) => U
): (result: Result<T, E>) => Result<U, E> {
  return (result) => {
    if (isOk(result)) {
      return ok(fn(result.value));
    }
    return result;
  };
}

/**
 * FlatMaps/chains a Result-returning function
 */
export function flatMap<T, U, E>(
  fn: (value: T) => Result<U, E>
): (result: Result<T, E>) => Result<U, E> {
  return (result) => {
    if (isOk(result)) {
      return fn(result.value);
    }
    return result;
  };
}

/**
 * Maps the error value of a Result
 */
export function mapError<T, E, F>(
  fn: (error: E) => F
): (result: Result<T, E>) => Result<T, F> {
  return (result) => {
    if (isErr(result)) {
      return err(fn(result.error));
    }
    return result;
  };
}

/**
 * Executes a side effect without changing the Result
 */
export function tap<T, E>(
  fn: (value: T) => void
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isOk(result)) {
      fn(result.value);
    }
    return result;
  };
}

/**
 * Executes a side effect on error without changing the Result
 */
export function tapError<T, E>(
  fn: (error: E) => void
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isErr(result)) {
      fn(result.error);
    }
    return result;
  };
}

/**
 * Recovers from an error by providing a default value
 */
export function recover<T, E>(
  fn: (error: E) => T
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isErr(result)) {
      return ok(fn(result.error));
    }
    return result;
  };
}

/**
 * Recovers from an error by providing a Result
 */
export function recoverWith<T, E>(
  fn: (error: E) => Result<T, E>
): (result: Result<T, E>) => Result<T, E> {
  return (result) => {
    if (isErr(result)) {
      return fn(result.error);
    }
    return result;
  };
}

/**
 * Type for a function that transforms a Result
 */
export type ResultTransformer<T, U, E, F = E> = (result: Result<T, E>) => Result<U, F>;

/**
 * Pipes a Result through a series of transformations
 * Stops at the first error encountered
 */
export function pipe<T, E>(
  result: Result<T, E>
): Result<T, E>;
export function pipe<T, U, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>
): Result<U, E>;
export function pipe<T, U, V, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>
): Result<V, E>;
export function pipe<T, U, V, W, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>
): Result<W, E>;
export function pipe<T, U, V, W, X, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>
): Result<X, E>;
export function pipe<T, U, V, W, X, Y, E>(
  result: Result<T, E>,
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>,
  fn5: ResultTransformer<X, Y, E>
): Result<Y, E>;
export function pipe(result: Result<any, any>, ...fns: Array<ResultTransformer<any, any, any>>): Result<any, any> {
  return fns.reduce((acc, fn) => {
    if (isErr(acc)) {
      return acc;
    }
    return fn(acc);
  }, result);
}

/**
 * Creates a pipeline factory that can be reused with different initial values
 */
export function createPipeline<T, U, E>(
  fn1: ResultTransformer<T, U, E>
): (result: Result<T, E>) => Result<U, E>;
export function createPipeline<T, U, V, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>
): (result: Result<T, E>) => Result<V, E>;
export function createPipeline<T, U, V, W, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>
): (result: Result<T, E>) => Result<W, E>;
export function createPipeline<T, U, V, W, X, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>
): (result: Result<T, E>) => Result<X, E>;
export function createPipeline<T, U, V, W, X, Y, E>(
  fn1: ResultTransformer<T, U, E>,
  fn2: ResultTransformer<U, V, E>,
  fn3: ResultTransformer<V, W, E>,
  fn4: ResultTransformer<W, X, E>,
  fn5: ResultTransformer<X, Y, E>
): (result: Result<T, E>) => Result<Y, E>;
export function createPipeline(...fns: Array<ResultTransformer<any, any, any>>): (result: Result<any, any>) => Result<any, any> {
  return (result) => pipe(result, ...fns);
}

/**
 * Wraps a function that might throw into a Result-returning function
 */
export function tryCatch<T, E = Error>(
  fn: () => T,
  errorHandler?: (error: unknown) => E
): Result<T, E> {
  try {
    return ok(fn());
  } catch (error) {
    const handledError = errorHandler ? errorHandler(error) : (error as E);
    return err(handledError);
  }
}

/**
 * Wraps an async function that might throw into a Result-returning async function
 */
export async function tryCatchAsync<T, E = Error>(
  fn: () => Promise<T>,
  errorHandler?: (error: unknown) => E
): Promise<Result<T, E>> {
  try {
    const value = await fn();
    return ok(value);
  } catch (error) {
    const handledError = errorHandler ? errorHandler(error) : (error as E);
    return err(handledError);
  }
}

/**
 * Combines multiple Results into a single Result containing an array
 * Returns the first error encountered, or success with all values
 */
export function combine<T, E>(results: Array<Result<T, E>>): Result<T[], E> {
  const values: T[] = [];

  for (const result of results) {
    if (isErr(result)) {
      return result;
    }
    values.push(result.value);
  }

  return ok(values);
}

/**
 * Gets the value from a Result or throws the error
 */
export function unwrap<T, E>(result: Result<T, E>): T {
  if (isOk(result)) {
    return result.value;
  }
  throw result.error;
}

/**
 * Gets the value from a Result or returns a default value
 */
export function unwrapOr<T, E>(defaultValue: T): (result: Result<T, E>) => T {
  return (result) => {
    if (isOk(result)) {
      return result.value;
    }
    return defaultValue;
  };
}