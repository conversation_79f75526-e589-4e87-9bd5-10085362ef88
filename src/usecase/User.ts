import {User,UserResponse, CreateUser,createUser} from "../domain/User";
import { depend } from 'velona'
import {Result,ok,err} from "../utils/Result";

export interface UserRepository {
  create: (user: User) => Promise<Result<User>>;
  update: (user: User) => Promise<Result<User>>;
  delete: (id: string) => Promise<Result<void>>;
  get: (id: string) => Promise<Result<User>>;
  getAll: () => Promise<Result<User[]>>;
}

export const SignUp = depend({} as {userRepository: UserRepository},
    async ({userRepository},name:string,email:string,password:string): Promise<Result<UserResponse>> => {

      const user = {
        name,
        email,
        password,
      }

      //ユーザーデータの検証
      const newUser = CreateUser.safeParse(user)

      if (!newUser.success) {
        return err(new Error("Invalid user data"))
      }

      //重複チェック
      const existingUser = await userRepository.get(user.email)

      if (existingUser.ok) {
        return err(new Error("User already exists"))
      }

      //ユーザーの作成
      const createdUser = createUser(newUser.data)

      const result = await userRepository.create(createdUser)

      if (!result.ok) {
        return err(new Error("Failed to create user"))
      }

      return result

    }
);
